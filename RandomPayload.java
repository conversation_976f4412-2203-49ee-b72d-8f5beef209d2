import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Random Payload Generator
 * Used to generate hexadecimal payloads for MQTT test data
 */
public class RandomPayload {

    /**
     * Helper method for generating random numbers (fixed-length Hex)
     * @param num The number to convert
     * @param digits The number of digits in the hexadecimal string
     * @return Fixed-length hexadecimal string
     */
    public static String hex(int num, int digits) {
        String hexStr = Integer.toHexString(num).toUpperCase();
        while (hexStr.length() < digits) {
            hexStr = "0" + hexStr;
        }
        return hexStr;
    }

    /**
     * Helper method for generating little-endian hexadecimal (for 2-byte values)
     * @param num The number to convert
     * @param digits The number of digits in the hexadecimal string (must be 4 for 2-byte little-endian)
     * @return Fixed-length little-endian hexadecimal string
     */
    public static String hexLittleEndian(int num, int digits) {
        if (digits != 4) {
            throw new IllegalArgumentException("Little-endian conversion only supports 4 digits (2 bytes)");
        }
        String hexStr = String.format("%04X", num);
        // 交换字节顺序：AABB -> BBAA
        return hexStr.substring(2) + hexStr.substring(0, 2);
    }

    /**
     * Generate random payload data
     * @return Map containing all generated data
     */
    public static Map<String, String> generateRandomPayload() {
        Map<String, String> vars = new HashMap<>();

        // Define East 8th Zone time range: 2025-12-01 ~ 2026-01-01
        ZoneId zone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime start = LocalDateTime.of(2025, 12, 1, 0, 0).atZone(zone);
        ZonedDateTime end = LocalDateTime.of(2026, 1, 1, 0, 0).atZone(zone);

        // Random UTC timestamp (in seconds)
        long randomSec = start.toEpochSecond() + (long)(Math.random() * (end.toEpochSecond() - start.toEpochSecond()));
        String randomUtc = String.format("%8s", Long.toHexString(randomSec).toUpperCase()).replace(' ', '0');

        // Random value definitions (you can adjust ranges as needed)
        int randomImei = (int)(Math.random() * 10000000);
        int randomIndex = (int)(Math.random() * 2882);
        int randomLongitude = (int)(Math.random() * 9000000) + 1000000;  // Longitude integer encoding
        int randomLatitude = (int)(Math.random() * 9000000) + 1000000;   // Latitude integer encoding
        int randomTtff = (int)(Math.random() * 15) + 1;                  // Positioning time 1-15 seconds
        int randomAltitude = (int)(Math.random() * 5000);                // Altitude
        int randomSpeed = (int)(Math.random() * 200);                    // Speed

        // Convert random values to hexadecimal and store
        vars.put("imeiHex", hex(randomImei, 6));
        vars.put("randomUtc", randomUtc);
        vars.put("indexHex", hex(randomIndex, 4));
        vars.put("longitudeHex", hex(randomLongitude, 8));
        vars.put("latitudeHex", hex(randomLatitude, 8));
        vars.put("ttffHex", hex(randomTtff, 2));
        vars.put("altitudeHex",  hexLittleEndian(randomAltitude, 4));
        vars.put("speedHex",  hexLittleEndian(randomSpeed, 4));

        // Build the final payload strings
        StringBuilder sb = new StringBuilder();
        sb.append("27")
          .append(vars.get("imeiHex"))
          .append("A6DE0102")
          .append(vars.get("randomUtc"))
          .append("55")
          .append(vars.get("indexHex"))
          .append(vars.get("longitudeHex"))
          .append(vars.get("latitudeHex"))
          .append(vars.get("ttffHex"))
          .append("0D1807121B")
          .append(vars.get("altitudeHex"))
          .append(vars.get("speedHex"));

        String payloadHex = sb.toString();
        vars.put("finalHexPayload", payloadHex);

        return vars;
    }

    /**
     * Main method - program entry point
     */
    public static void main(String[] args) {
        System.out.println("===== Random Payload Generator =====");

        // Generate random payload data
        Map<String, String> payloadData = generateRandomPayload();

        // Output generated data
        System.out.println("Generated random data:");
        System.out.println("IMEI (Hex):" + payloadData.get("imeiHex"));
        System.out.println("UTC Timestamp (Hex):" + payloadData.get("randomUtc"));
        System.out.println("Index (Hex):" + payloadData.get("indexHex"));
        System.out.println("Longitude (Hex):" + payloadData.get("longitudeHex"));
        System.out.println("Latitude (Hex):" + payloadData.get("latitudeHex"));
        System.out.println("TTFF (Hex):" + payloadData.get("ttffHex"));
        System.out.println("Altitude (Hex):" + payloadData.get("altitudeHex"));
        System.out.println("Speed (Hex):" + payloadData.get("speedHex"));
        System.out.println();
        System.out.println("Final payload (Hex):" + payloadData.get("finalHexPayload"));
        System.out.println("Payload length:" + payloadData.get("finalHexPayload").length() + " characters");
    }
}
