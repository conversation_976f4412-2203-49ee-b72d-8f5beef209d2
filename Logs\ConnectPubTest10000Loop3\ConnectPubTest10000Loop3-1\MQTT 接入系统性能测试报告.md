# MQTT 接入系统性能测试报告

------

## 一、测试说明

### 1.1 测试目的

本次测试旨在验证 MQTT 接入服务在多轮不同负载场景下的性能稳定性，评估系统的响应时间、并发处理能力和处理效率，为生产环境上线提供参考依据。

### 1.2 测试范围

测试内容涵盖 MQTT 连接（Connect）、消息发布（Publish）、断开连接（Disconnect）三个核心操作的性能表现。所有测试均在服务正常运行的基础上执行，并以模拟设备行为为主。

### 1.3 测试人员

- 测试工程师：XXX
- 测试时间：2025年7月28日
- 测试环境：MQTT Broker 正常运行，无其他并发干扰，使用 JMeter 脚本自动化执行。

------

## 二、测试方案

### 2.1 测试工具

- 工具名称：Apache JMeter 3.2
- 插件：MQTT 插件（HiveMQTT）
- 数据文件：publish_detailed.jtl，publish_detailed-2.jtl，publish_detailed-3.jtl

### 2.2 测试前置条件

- MQTT 服务端部署完成并稳定运行；
- 服务端具备高并发连接能力；
- 预设 MQTT Topic、Client ID、Payload 模板已配置；
- 后端解析与存储模块尚未全面接入，仅验证了传输链路的有效性。

------

## 三、测试记录

### 3.1 测试1 - 高延迟场景验证

- 起止时间：2025-07-28 08:28:35 至 08:28:52
- 并发线程：约 10,000
- 总请求数：30,000（Connect/Publish/Disconnect 各 10,000）

| 请求类型        | 请求数 | 成功数 | 平均响应(ms) | 最大(ms) | 最小(ms) | 成功率 |
| --------------- | ------ | ------ | ------------ | -------- | -------- | ------ |
| MQTT Connect    | 10,000 | 10,000 | 9667.41      | 16,397   | 3,394    | 100%   |
| MQTT Publish    | 10,000 | 10,000 | 8.17         | 318      | 0        | 100%   |
| MQTT Disconnect | 10,000 | 10,000 | 1,043.46     | 3,627    | 12       | 100%   |

> 🔎 **说明**：首次测试发现 MQTT Connect 耗时较高，说明在启动大规模连接时服务端承载压力明显。Publish 过程性能良好。

------

### 3.2 测试2 - 中等延迟、快速启动验证

- 起止时间：2025-07-28 08:30:55 至 08:31:05
- 并发线程：约 10,000
- 总请求数：30,000

| 请求类型        | 请求数 | 成功数 | 平均响应(ms) | 最大(ms) | 最小(ms) | 成功率 |
| --------------- | ------ | ------ | ------------ | -------- | -------- | ------ |
| MQTT Connect    | 10,000 | 10,000 | 323.96       | 1,781    | 21       | 100%   |
| MQTT Publish    | 10,000 | 10,000 | 1.36         | 173      | 0        | 100%   |
| MQTT Disconnect | 10,000 | 10,000 | 131.67       | 1,113    | 10       | 100%   |

> 🔎 **说明**：Connect 响应显著加快，Publish 延迟进一步降低。整体吞吐表现优异，系统压力有所缓解。

------

### 3.3 测试3 - 最佳性能验证

- 起止时间：2025-07-28 08:34:51 至 08:35:02
- 并发线程：约 10,000
- 总请求数：30,000

| 请求类型        | 请求数 | 成功数 | 平均响应(ms) | 最大(ms) | 最小(ms) | 成功率 |
| --------------- | ------ | ------ | ------------ | -------- | -------- | ------ |
| MQTT Connect    | 10,000 | 10,000 | 252.69       | 1,318    | 22       | 100%   |
| MQTT Publish    | 10,000 | 10,000 | 0.34         | 51       | 0        | 100%   |
| MQTT Disconnect | 10,000 | 10,000 | 126.21       | 727      | 10       | 100%   |

> 🔎 **说明**：系统进入最优状态，所有操作响应时间最低。MQTT Publish 几乎无延迟，服务具备高并发接入与快速发布的能力。

------

## 四、测试总结

### 4.1 测试结果概述

| 轮次  | 请求总数 | 成功数 | 成功率  | 平均持续时间 |
| ----- | -------- | ------ | ------- | ------------ |
| 测试1 | 30,000   | 30,000 | 100.00% | 16.62 秒     |
| 测试2 | 30,000   | 30,000 | 100.00% | 10.38 秒     |
| 测试3 | 30,000   | 30,000 | 100.00% | 10.58 秒     |

### 4.2 测试结论

✅ **优点**：

- 所有测试均未出现失败，成功率 100%，系统稳定性好；
- MQTT Connect、Publish、Disconnect 表现良好，特别是 Publish 延迟极低；
- 系统对 10,000 并发线程处理能力达标，吞吐量充足。

⚠️ **不足/问题**：

- **测试过程中虽然 Publish 报文已被成功发送，但后端报文解析与数据库入库流程尚未完整打通**；
- **真实业务处理流程未得到全链路验证，可能存在消息只被 MQTT Broker 接收但未存储的风险**；
- MQTT Connect 初期响应时间偏高（测试1），需要进一步优化线程池初始化与连接建链策略。

📌 **建议**：

1. **构建“全链路”压测方案，确保从接收 → 解析 → 存储流程全部正常运行**；
2. 增加后端日志验证与数据库状态比对，确保消息确实落库；
3. 扩展测试内容，包括 QoS 1/2、遗嘱消息、订阅回执等业务特性；
4. 在弱网、断网等极端场景下重复验证系统鲁棒性；
5. 开启性能监控工具（如 Prometheus + Grafana）记录 CPU、内存、连接池状态。

------

## 五、附件

- JMeter 脚本配置
- 原始测试数据 `.jtl` 文件
- MQTT Broker 日志摘要
- 服务器性能监控截图（如有）

------

如需将本报告导出为 Word 或 PDF 并套用正式模板，请告诉我导出格式与公司模板要求，我可为你生成正式版本。是否继续导出文档？