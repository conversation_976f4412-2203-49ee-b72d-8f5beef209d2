/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 8.0, "minX": 0.0, "maxY": 875.0, "series": [{"data": [[0.0, 8.0], [0.1, 8.0], [0.2, 8.0], [0.3, 8.0], [0.4, 8.0], [0.5, 8.0], [0.6, 8.0], [0.7, 9.0], [0.8, 9.0], [0.9, 9.0], [1.0, 9.0], [1.1, 9.0], [1.2, 9.0], [1.3, 9.0], [1.4, 9.0], [1.5, 9.0], [1.6, 9.0], [1.7, 9.0], [1.8, 10.0], [1.9, 10.0], [2.0, 10.0], [2.1, 10.0], [2.2, 10.0], [2.3, 10.0], [2.4, 10.0], [2.5, 10.0], [2.6, 10.0], [2.7, 10.0], [2.8, 10.0], [2.9, 10.0], [3.0, 10.0], [3.1, 10.0], [3.2, 10.0], [3.3, 10.0], [3.4, 10.0], [3.5, 10.0], [3.6, 10.0], [3.7, 10.0], [3.8, 10.0], [3.9, 10.0], [4.0, 10.0], [4.1, 10.0], [4.2, 10.0], [4.3, 10.0], [4.4, 10.0], [4.5, 10.0], [4.6, 10.0], [4.7, 11.0], [4.8, 11.0], [4.9, 11.0], [5.0, 11.0], [5.1, 11.0], [5.2, 11.0], [5.3, 11.0], [5.4, 11.0], [5.5, 11.0], [5.6, 11.0], [5.7, 11.0], [5.8, 11.0], [5.9, 11.0], [6.0, 11.0], [6.1, 11.0], [6.2, 11.0], [6.3, 11.0], [6.4, 11.0], [6.5, 11.0], [6.6, 11.0], [6.7, 11.0], [6.8, 11.0], [6.9, 11.0], [7.0, 11.0], [7.1, 11.0], [7.2, 11.0], [7.3, 11.0], [7.4, 11.0], [7.5, 11.0], [7.6, 11.0], [7.7, 11.0], [7.8, 11.0], [7.9, 11.0], [8.0, 11.0], [8.1, 11.0], [8.2, 11.0], [8.3, 11.0], [8.4, 11.0], [8.5, 11.0], [8.6, 11.0], [8.7, 11.0], [8.8, 11.0], [8.9, 11.0], [9.0, 11.0], [9.1, 11.0], [9.2, 11.0], [9.3, 11.0], [9.4, 11.0], [9.5, 11.0], [9.6, 11.0], [9.7, 11.0], [9.8, 11.0], [9.9, 11.0], [10.0, 11.0], [10.1, 11.0], [10.2, 11.0], [10.3, 11.0], [10.4, 11.0], [10.5, 11.0], [10.6, 11.0], [10.7, 11.0], [10.8, 11.0], [10.9, 11.0], [11.0, 11.0], [11.1, 11.0], [11.2, 11.0], [11.3, 11.0], [11.4, 11.0], [11.5, 11.0], [11.6, 11.0], [11.7, 12.0], [11.8, 12.0], [11.9, 12.0], [12.0, 12.0], [12.1, 12.0], [12.2, 12.0], [12.3, 12.0], [12.4, 12.0], [12.5, 12.0], [12.6, 12.0], [12.7, 12.0], [12.8, 12.0], [12.9, 12.0], [13.0, 12.0], [13.1, 12.0], [13.2, 12.0], [13.3, 12.0], [13.4, 12.0], [13.5, 12.0], [13.6, 12.0], [13.7, 12.0], [13.8, 12.0], [13.9, 12.0], [14.0, 12.0], [14.1, 12.0], [14.2, 12.0], [14.3, 12.0], [14.4, 12.0], [14.5, 12.0], [14.6, 12.0], [14.7, 12.0], [14.8, 12.0], [14.9, 12.0], [15.0, 12.0], [15.1, 12.0], [15.2, 12.0], [15.3, 12.0], [15.4, 12.0], [15.5, 12.0], [15.6, 12.0], [15.7, 12.0], [15.8, 12.0], [15.9, 12.0], [16.0, 12.0], [16.1, 12.0], [16.2, 12.0], [16.3, 12.0], [16.4, 12.0], [16.5, 12.0], [16.6, 12.0], [16.7, 12.0], [16.8, 12.0], [16.9, 12.0], [17.0, 12.0], [17.1, 12.0], [17.2, 12.0], [17.3, 12.0], [17.4, 12.0], [17.5, 12.0], [17.6, 12.0], [17.7, 12.0], [17.8, 12.0], [17.9, 12.0], [18.0, 12.0], [18.1, 12.0], [18.2, 12.0], [18.3, 12.0], [18.4, 12.0], [18.5, 12.0], [18.6, 12.0], [18.7, 12.0], [18.8, 12.0], [18.9, 12.0], [19.0, 12.0], [19.1, 12.0], [19.2, 12.0], [19.3, 12.0], [19.4, 12.0], [19.5, 13.0], [19.6, 13.0], [19.7, 13.0], [19.8, 13.0], [19.9, 13.0], [20.0, 13.0], [20.1, 13.0], [20.2, 13.0], [20.3, 13.0], [20.4, 13.0], [20.5, 13.0], [20.6, 13.0], [20.7, 13.0], [20.8, 13.0], [20.9, 13.0], [21.0, 13.0], [21.1, 13.0], [21.2, 13.0], [21.3, 13.0], [21.4, 13.0], [21.5, 13.0], [21.6, 13.0], [21.7, 13.0], [21.8, 13.0], [21.9, 13.0], [22.0, 13.0], [22.1, 13.0], [22.2, 13.0], [22.3, 13.0], [22.4, 13.0], [22.5, 13.0], [22.6, 13.0], [22.7, 13.0], [22.8, 13.0], [22.9, 13.0], [23.0, 13.0], [23.1, 13.0], [23.2, 13.0], [23.3, 13.0], [23.4, 13.0], [23.5, 13.0], [23.6, 13.0], [23.7, 13.0], [23.8, 13.0], [23.9, 13.0], [24.0, 13.0], [24.1, 13.0], [24.2, 13.0], [24.3, 13.0], [24.4, 13.0], [24.5, 13.0], [24.6, 13.0], [24.7, 13.0], [24.8, 13.0], [24.9, 13.0], [25.0, 13.0], [25.1, 13.0], [25.2, 13.0], [25.3, 13.0], [25.4, 13.0], [25.5, 13.0], [25.6, 13.0], [25.7, 13.0], [25.8, 13.0], [25.9, 13.0], [26.0, 13.0], [26.1, 13.0], [26.2, 13.0], [26.3, 13.0], [26.4, 13.0], [26.5, 13.0], [26.6, 13.0], [26.7, 13.0], [26.8, 13.0], [26.9, 13.0], [27.0, 13.0], [27.1, 13.0], [27.2, 13.0], [27.3, 13.0], [27.4, 13.0], [27.5, 13.0], [27.6, 13.0], [27.7, 13.0], [27.8, 13.0], [27.9, 13.0], [28.0, 13.0], [28.1, 13.0], [28.2, 13.0], [28.3, 13.0], [28.4, 13.0], [28.5, 13.0], [28.6, 13.0], [28.7, 13.0], [28.8, 13.0], [28.9, 13.0], [29.0, 13.0], [29.1, 13.0], [29.2, 13.0], [29.3, 13.0], [29.4, 13.0], [29.5, 13.0], [29.6, 13.0], [29.7, 13.0], [29.8, 13.0], [29.9, 13.0], [30.0, 13.0], [30.1, 13.0], [30.2, 13.0], [30.3, 13.0], [30.4, 13.0], [30.5, 13.0], [30.6, 13.0], [30.7, 13.0], [30.8, 13.0], [30.9, 13.0], [31.0, 13.0], [31.1, 13.0], [31.2, 13.0], [31.3, 13.0], [31.4, 13.0], [31.5, 13.0], [31.6, 13.0], [31.7, 13.0], [31.8, 13.0], [31.9, 14.0], [32.0, 14.0], [32.1, 14.0], [32.2, 14.0], [32.3, 14.0], [32.4, 14.0], [32.5, 14.0], [32.6, 14.0], [32.7, 14.0], [32.8, 14.0], [32.9, 14.0], [33.0, 14.0], [33.1, 14.0], [33.2, 14.0], [33.3, 14.0], [33.4, 14.0], [33.5, 14.0], [33.6, 14.0], [33.7, 14.0], [33.8, 14.0], [33.9, 14.0], [34.0, 14.0], [34.1, 14.0], [34.2, 14.0], [34.3, 14.0], [34.4, 14.0], [34.5, 14.0], [34.6, 14.0], [34.7, 14.0], [34.8, 14.0], [34.9, 14.0], [35.0, 14.0], [35.1, 14.0], [35.2, 14.0], [35.3, 14.0], [35.4, 14.0], [35.5, 14.0], [35.6, 14.0], [35.7, 14.0], [35.8, 14.0], [35.9, 14.0], [36.0, 14.0], [36.1, 14.0], [36.2, 14.0], [36.3, 14.0], [36.4, 14.0], [36.5, 14.0], [36.6, 14.0], [36.7, 14.0], [36.8, 14.0], [36.9, 14.0], [37.0, 14.0], [37.1, 14.0], [37.2, 14.0], [37.3, 14.0], [37.4, 14.0], [37.5, 14.0], [37.6, 14.0], [37.7, 14.0], [37.8, 14.0], [37.9, 14.0], [38.0, 14.0], [38.1, 14.0], [38.2, 14.0], [38.3, 14.0], [38.4, 14.0], [38.5, 14.0], [38.6, 14.0], [38.7, 14.0], [38.8, 14.0], [38.9, 14.0], [39.0, 14.0], [39.1, 14.0], [39.2, 14.0], [39.3, 14.0], [39.4, 14.0], [39.5, 14.0], [39.6, 14.0], [39.7, 14.0], [39.8, 14.0], [39.9, 14.0], [40.0, 14.0], [40.1, 14.0], [40.2, 14.0], [40.3, 14.0], [40.4, 14.0], [40.5, 14.0], [40.6, 14.0], [40.7, 14.0], [40.8, 14.0], [40.9, 14.0], [41.0, 14.0], [41.1, 14.0], [41.2, 14.0], [41.3, 14.0], [41.4, 14.0], [41.5, 14.0], [41.6, 14.0], [41.7, 14.0], [41.8, 14.0], [41.9, 14.0], [42.0, 14.0], [42.1, 14.0], [42.2, 14.0], [42.3, 14.0], [42.4, 14.0], [42.5, 14.0], [42.6, 14.0], [42.7, 14.0], [42.8, 14.0], [42.9, 14.0], [43.0, 14.0], [43.1, 14.0], [43.2, 14.0], [43.3, 14.0], [43.4, 14.0], [43.5, 14.0], [43.6, 14.0], [43.7, 14.0], [43.8, 14.0], [43.9, 14.0], [44.0, 14.0], [44.1, 15.0], [44.2, 15.0], [44.3, 15.0], [44.4, 15.0], [44.5, 15.0], [44.6, 15.0], [44.7, 15.0], [44.8, 15.0], [44.9, 15.0], [45.0, 15.0], [45.1, 15.0], [45.2, 15.0], [45.3, 15.0], [45.4, 15.0], [45.5, 15.0], [45.6, 15.0], [45.7, 15.0], [45.8, 15.0], [45.9, 15.0], [46.0, 15.0], [46.1, 15.0], [46.2, 15.0], [46.3, 15.0], [46.4, 15.0], [46.5, 15.0], [46.6, 15.0], [46.7, 15.0], [46.8, 15.0], [46.9, 15.0], [47.0, 15.0], [47.1, 15.0], [47.2, 15.0], [47.3, 15.0], [47.4, 15.0], [47.5, 15.0], [47.6, 15.0], [47.7, 15.0], [47.8, 15.0], [47.9, 15.0], [48.0, 15.0], [48.1, 15.0], [48.2, 15.0], [48.3, 15.0], [48.4, 15.0], [48.5, 15.0], [48.6, 15.0], [48.7, 15.0], [48.8, 15.0], [48.9, 15.0], [49.0, 15.0], [49.1, 15.0], [49.2, 15.0], [49.3, 15.0], [49.4, 15.0], [49.5, 15.0], [49.6, 15.0], [49.7, 15.0], [49.8, 15.0], [49.9, 15.0], [50.0, 15.0], [50.1, 15.0], [50.2, 15.0], [50.3, 15.0], [50.4, 15.0], [50.5, 15.0], [50.6, 15.0], [50.7, 15.0], [50.8, 15.0], [50.9, 15.0], [51.0, 15.0], [51.1, 15.0], [51.2, 15.0], [51.3, 15.0], [51.4, 15.0], [51.5, 15.0], [51.6, 15.0], [51.7, 15.0], [51.8, 15.0], [51.9, 15.0], [52.0, 15.0], [52.1, 15.0], [52.2, 15.0], [52.3, 15.0], [52.4, 15.0], [52.5, 15.0], [52.6, 15.0], [52.7, 15.0], [52.8, 15.0], [52.9, 15.0], [53.0, 15.0], [53.1, 15.0], [53.2, 15.0], [53.3, 15.0], [53.4, 15.0], [53.5, 15.0], [53.6, 15.0], [53.7, 15.0], [53.8, 15.0], [53.9, 15.0], [54.0, 15.0], [54.1, 15.0], [54.2, 15.0], [54.3, 15.0], [54.4, 15.0], [54.5, 15.0], [54.6, 15.0], [54.7, 15.0], [54.8, 15.0], [54.9, 15.0], [55.0, 15.0], [55.1, 15.0], [55.2, 15.0], [55.3, 15.0], [55.4, 15.0], [55.5, 15.0], [55.6, 15.0], [55.7, 15.0], [55.8, 15.0], [55.9, 15.0], [56.0, 15.0], [56.1, 15.0], [56.2, 15.0], [56.3, 15.0], [56.4, 15.0], [56.5, 15.0], [56.6, 15.0], [56.7, 15.0], [56.8, 15.0], [56.9, 15.0], [57.0, 15.0], [57.1, 15.0], [57.2, 15.0], [57.3, 15.0], [57.4, 15.0], [57.5, 15.0], [57.6, 15.0], [57.7, 15.0], [57.8, 15.0], [57.9, 15.0], [58.0, 15.0], [58.1, 15.0], [58.2, 15.0], [58.3, 15.0], [58.4, 15.0], [58.5, 15.0], [58.6, 15.0], [58.7, 15.0], [58.8, 15.0], [58.9, 15.0], [59.0, 15.0], [59.1, 15.0], [59.2, 15.0], [59.3, 16.0], [59.4, 16.0], [59.5, 16.0], [59.6, 16.0], [59.7, 16.0], [59.8, 16.0], [59.9, 16.0], [60.0, 16.0], [60.1, 16.0], [60.2, 16.0], [60.3, 16.0], [60.4, 16.0], [60.5, 16.0], [60.6, 16.0], [60.7, 16.0], [60.8, 16.0], [60.9, 16.0], [61.0, 16.0], [61.1, 16.0], [61.2, 16.0], [61.3, 16.0], [61.4, 16.0], [61.5, 16.0], [61.6, 16.0], [61.7, 16.0], [61.8, 16.0], [61.9, 16.0], [62.0, 16.0], [62.1, 16.0], [62.2, 16.0], [62.3, 16.0], [62.4, 16.0], [62.5, 16.0], [62.6, 16.0], [62.7, 16.0], [62.8, 16.0], [62.9, 16.0], [63.0, 16.0], [63.1, 16.0], [63.2, 16.0], [63.3, 16.0], [63.4, 16.0], [63.5, 16.0], [63.6, 16.0], [63.7, 16.0], [63.8, 16.0], [63.9, 16.0], [64.0, 16.0], [64.1, 16.0], [64.2, 16.0], [64.3, 16.0], [64.4, 16.0], [64.5, 16.0], [64.6, 16.0], [64.7, 16.0], [64.8, 16.0], [64.9, 16.0], [65.0, 16.0], [65.1, 16.0], [65.2, 16.0], [65.3, 16.0], [65.4, 16.0], [65.5, 16.0], [65.6, 16.0], [65.7, 16.0], [65.8, 16.0], [65.9, 16.0], [66.0, 16.0], [66.1, 16.0], [66.2, 16.0], [66.3, 16.0], [66.4, 16.0], [66.5, 16.0], [66.6, 16.0], [66.7, 16.0], [66.8, 16.0], [66.9, 16.0], [67.0, 16.0], [67.1, 16.0], [67.2, 16.0], [67.3, 16.0], [67.4, 16.0], [67.5, 16.0], [67.6, 16.0], [67.7, 16.0], [67.8, 16.0], [67.9, 16.0], [68.0, 16.0], [68.1, 16.0], [68.2, 16.0], [68.3, 16.0], [68.4, 16.0], [68.5, 16.0], [68.6, 16.0], [68.7, 17.0], [68.8, 17.0], [68.9, 17.0], [69.0, 17.0], [69.1, 17.0], [69.2, 17.0], [69.3, 17.0], [69.4, 17.0], [69.5, 17.0], [69.6, 17.0], [69.7, 17.0], [69.8, 17.0], [69.9, 17.0], [70.0, 17.0], [70.1, 17.0], [70.2, 17.0], [70.3, 17.0], [70.4, 17.0], [70.5, 17.0], [70.6, 17.0], [70.7, 17.0], [70.8, 17.0], [70.9, 17.0], [71.0, 17.0], [71.1, 17.0], [71.2, 17.0], [71.3, 17.0], [71.4, 17.0], [71.5, 17.0], [71.6, 17.0], [71.7, 17.0], [71.8, 17.0], [71.9, 17.0], [72.0, 17.0], [72.1, 17.0], [72.2, 17.0], [72.3, 17.0], [72.4, 17.0], [72.5, 17.0], [72.6, 17.0], [72.7, 17.0], [72.8, 17.0], [72.9, 17.0], [73.0, 17.0], [73.1, 17.0], [73.2, 17.0], [73.3, 17.0], [73.4, 17.0], [73.5, 17.0], [73.6, 17.0], [73.7, 17.0], [73.8, 17.0], [73.9, 17.0], [74.0, 17.0], [74.1, 17.0], [74.2, 17.0], [74.3, 17.0], [74.4, 17.0], [74.5, 17.0], [74.6, 17.0], [74.7, 17.0], [74.8, 17.0], [74.9, 17.0], [75.0, 17.0], [75.1, 17.0], [75.2, 17.0], [75.3, 17.0], [75.4, 17.0], [75.5, 17.0], [75.6, 17.0], [75.7, 17.0], [75.8, 17.0], [75.9, 17.0], [76.0, 17.0], [76.1, 17.0], [76.2, 17.0], [76.3, 17.0], [76.4, 17.0], [76.5, 17.0], [76.6, 17.0], [76.7, 17.0], [76.8, 17.0], [76.9, 17.0], [77.0, 17.0], [77.1, 17.0], [77.2, 17.0], [77.3, 17.0], [77.4, 17.0], [77.5, 17.0], [77.6, 17.0], [77.7, 17.0], [77.8, 17.0], [77.9, 17.0], [78.0, 18.0], [78.1, 18.0], [78.2, 18.0], [78.3, 18.0], [78.4, 18.0], [78.5, 18.0], [78.6, 18.0], [78.7, 18.0], [78.8, 18.0], [78.9, 18.0], [79.0, 18.0], [79.1, 18.0], [79.2, 18.0], [79.3, 18.0], [79.4, 18.0], [79.5, 18.0], [79.6, 18.0], [79.7, 18.0], [79.8, 18.0], [79.9, 18.0], [80.0, 18.0], [80.1, 18.0], [80.2, 18.0], [80.3, 18.0], [80.4, 18.0], [80.5, 18.0], [80.6, 18.0], [80.7, 18.0], [80.8, 18.0], [80.9, 18.0], [81.0, 18.0], [81.1, 18.0], [81.2, 18.0], [81.3, 18.0], [81.4, 18.0], [81.5, 18.0], [81.6, 18.0], [81.7, 18.0], [81.8, 18.0], [81.9, 18.0], [82.0, 18.0], [82.1, 18.0], [82.2, 18.0], [82.3, 18.0], [82.4, 18.0], [82.5, 18.0], [82.6, 18.0], [82.7, 18.0], [82.8, 18.0], [82.9, 18.0], [83.0, 18.0], [83.1, 18.0], [83.2, 18.0], [83.3, 18.0], [83.4, 18.0], [83.5, 18.0], [83.6, 18.0], [83.7, 18.0], [83.8, 18.0], [83.9, 19.0], [84.0, 19.0], [84.1, 19.0], [84.2, 19.0], [84.3, 19.0], [84.4, 19.0], [84.5, 19.0], [84.6, 19.0], [84.7, 19.0], [84.8, 19.0], [84.9, 19.0], [85.0, 19.0], [85.1, 19.0], [85.2, 19.0], [85.3, 19.0], [85.4, 19.0], [85.5, 19.0], [85.6, 19.0], [85.7, 19.0], [85.8, 19.0], [85.9, 19.0], [86.0, 19.0], [86.1, 19.0], [86.2, 19.0], [86.3, 19.0], [86.4, 19.0], [86.5, 19.0], [86.6, 19.0], [86.7, 19.0], [86.8, 19.0], [86.9, 19.0], [87.0, 19.0], [87.1, 19.0], [87.2, 19.0], [87.3, 19.0], [87.4, 19.0], [87.5, 19.0], [87.6, 19.0], [87.7, 20.0], [87.8, 20.0], [87.9, 20.0], [88.0, 20.0], [88.1, 20.0], [88.2, 20.0], [88.3, 20.0], [88.4, 20.0], [88.5, 20.0], [88.6, 20.0], [88.7, 20.0], [88.8, 20.0], [88.9, 20.0], [89.0, 20.0], [89.1, 21.0], [89.2, 21.0], [89.3, 22.0], [89.4, 22.0], [89.5, 22.0], [89.6, 22.0], [89.7, 23.0], [89.8, 24.0], [89.9, 25.0], [90.0, 25.0], [90.1, 26.0], [90.2, 26.0], [90.3, 26.0], [90.4, 28.0], [90.5, 29.0], [90.6, 29.0], [90.7, 29.0], [90.8, 29.0], [90.9, 29.0], [91.0, 29.0], [91.1, 29.0], [91.2, 30.0], [91.3, 31.0], [91.4, 32.0], [91.5, 32.0], [91.6, 32.0], [91.7, 33.0], [91.8, 33.0], [91.9, 35.0], [92.0, 35.0], [92.1, 35.0], [92.2, 37.0], [92.3, 37.0], [92.4, 37.0], [92.5, 37.0], [92.6, 37.0], [92.7, 38.0], [92.8, 38.0], [92.9, 38.0], [93.0, 38.0], [93.1, 43.0], [93.2, 47.0], [93.3, 47.0], [93.4, 47.0], [93.5, 47.0], [93.6, 47.0], [93.7, 47.0], [93.8, 48.0], [93.9, 49.0], [94.0, 50.0], [94.1, 56.0], [94.2, 56.0], [94.3, 61.0], [94.4, 63.0], [94.5, 63.0], [94.6, 63.0], [94.7, 64.0], [94.8, 65.0], [94.9, 66.0], [95.0, 66.0], [95.1, 66.0], [95.2, 67.0], [95.3, 68.0], [95.4, 71.0], [95.5, 71.0], [95.6, 71.0], [95.7, 72.0], [95.8, 73.0], [95.9, 73.0], [96.0, 74.0], [96.1, 74.0], [96.2, 76.0], [96.3, 77.0], [96.4, 77.0], [96.5, 78.0], [96.6, 78.0], [96.7, 79.0], [96.8, 79.0], [96.9, 79.0], [97.0, 80.0], [97.1, 80.0], [97.2, 80.0], [97.3, 81.0], [97.4, 81.0], [97.5, 82.0], [97.6, 82.0], [97.7, 83.0], [97.8, 84.0], [97.9, 84.0], [98.0, 84.0], [98.1, 84.0], [98.2, 85.0], [98.3, 85.0], [98.4, 86.0], [98.5, 86.0], [98.6, 87.0], [98.7, 87.0], [98.8, 87.0], [98.9, 87.0], [99.0, 87.0], [99.1, 88.0], [99.2, 89.0], [99.3, 89.0], [99.4, 90.0], [99.5, 90.0], [99.6, 91.0], [99.7, 91.0], [99.8, 92.0], [99.9, 93.0]], "isOverall": false, "label": "MQTT Disconnect", "isController": false}, {"data": [[0.0, 16.0], [0.1, 16.0], [0.2, 16.0], [0.3, 17.0], [0.4, 17.0], [0.5, 17.0], [0.6, 17.0], [0.7, 18.0], [0.8, 18.0], [0.9, 18.0], [1.0, 18.0], [1.1, 18.0], [1.2, 18.0], [1.3, 18.0], [1.4, 19.0], [1.5, 19.0], [1.6, 19.0], [1.7, 19.0], [1.8, 19.0], [1.9, 19.0], [2.0, 19.0], [2.1, 19.0], [2.2, 19.0], [2.3, 19.0], [2.4, 19.0], [2.5, 19.0], [2.6, 19.0], [2.7, 20.0], [2.8, 20.0], [2.9, 20.0], [3.0, 20.0], [3.1, 20.0], [3.2, 20.0], [3.3, 20.0], [3.4, 20.0], [3.5, 21.0], [3.6, 21.0], [3.7, 21.0], [3.8, 21.0], [3.9, 21.0], [4.0, 21.0], [4.1, 21.0], [4.2, 21.0], [4.3, 21.0], [4.4, 21.0], [4.5, 21.0], [4.6, 21.0], [4.7, 21.0], [4.8, 21.0], [4.9, 21.0], [5.0, 21.0], [5.1, 21.0], [5.2, 21.0], [5.3, 21.0], [5.4, 21.0], [5.5, 21.0], [5.6, 21.0], [5.7, 22.0], [5.8, 22.0], [5.9, 22.0], [6.0, 22.0], [6.1, 22.0], [6.2, 22.0], [6.3, 22.0], [6.4, 22.0], [6.5, 22.0], [6.6, 22.0], [6.7, 22.0], [6.8, 22.0], [6.9, 22.0], [7.0, 22.0], [7.1, 22.0], [7.2, 22.0], [7.3, 22.0], [7.4, 22.0], [7.5, 22.0], [7.6, 22.0], [7.7, 22.0], [7.8, 22.0], [7.9, 22.0], [8.0, 22.0], [8.1, 22.0], [8.2, 22.0], [8.3, 22.0], [8.4, 23.0], [8.5, 23.0], [8.6, 23.0], [8.7, 23.0], [8.8, 23.0], [8.9, 23.0], [9.0, 23.0], [9.1, 23.0], [9.2, 23.0], [9.3, 23.0], [9.4, 23.0], [9.5, 23.0], [9.6, 23.0], [9.7, 23.0], [9.8, 23.0], [9.9, 23.0], [10.0, 23.0], [10.1, 23.0], [10.2, 23.0], [10.3, 23.0], [10.4, 23.0], [10.5, 23.0], [10.6, 23.0], [10.7, 23.0], [10.8, 23.0], [10.9, 23.0], [11.0, 23.0], [11.1, 23.0], [11.2, 24.0], [11.3, 24.0], [11.4, 24.0], [11.5, 24.0], [11.6, 24.0], [11.7, 24.0], [11.8, 24.0], [11.9, 24.0], [12.0, 24.0], [12.1, 24.0], [12.2, 24.0], [12.3, 24.0], [12.4, 24.0], [12.5, 24.0], [12.6, 24.0], [12.7, 24.0], [12.8, 24.0], [12.9, 24.0], [13.0, 24.0], [13.1, 24.0], [13.2, 24.0], [13.3, 24.0], [13.4, 24.0], [13.5, 24.0], [13.6, 24.0], [13.7, 24.0], [13.8, 24.0], [13.9, 24.0], [14.0, 24.0], [14.1, 24.0], [14.2, 24.0], [14.3, 24.0], [14.4, 24.0], [14.5, 24.0], [14.6, 24.0], [14.7, 24.0], [14.8, 24.0], [14.9, 24.0], [15.0, 24.0], [15.1, 24.0], [15.2, 24.0], [15.3, 24.0], [15.4, 24.0], [15.5, 24.0], [15.6, 24.0], [15.7, 24.0], [15.8, 24.0], [15.9, 24.0], [16.0, 24.0], [16.1, 24.0], [16.2, 24.0], [16.3, 24.0], [16.4, 24.0], [16.5, 24.0], [16.6, 25.0], [16.7, 25.0], [16.8, 25.0], [16.9, 25.0], [17.0, 25.0], [17.1, 25.0], [17.2, 25.0], [17.3, 25.0], [17.4, 25.0], [17.5, 25.0], [17.6, 25.0], [17.7, 25.0], [17.8, 25.0], [17.9, 25.0], [18.0, 25.0], [18.1, 25.0], [18.2, 25.0], [18.3, 25.0], [18.4, 25.0], [18.5, 25.0], [18.6, 25.0], [18.7, 25.0], [18.8, 25.0], [18.9, 25.0], [19.0, 25.0], [19.1, 25.0], [19.2, 25.0], [19.3, 25.0], [19.4, 25.0], [19.5, 25.0], [19.6, 25.0], [19.7, 25.0], [19.8, 25.0], [19.9, 25.0], [20.0, 25.0], [20.1, 25.0], [20.2, 25.0], [20.3, 25.0], [20.4, 25.0], [20.5, 26.0], [20.6, 26.0], [20.7, 26.0], [20.8, 26.0], [20.9, 26.0], [21.0, 26.0], [21.1, 26.0], [21.2, 26.0], [21.3, 26.0], [21.4, 26.0], [21.5, 26.0], [21.6, 26.0], [21.7, 26.0], [21.8, 26.0], [21.9, 26.0], [22.0, 26.0], [22.1, 26.0], [22.2, 26.0], [22.3, 26.0], [22.4, 26.0], [22.5, 26.0], [22.6, 26.0], [22.7, 26.0], [22.8, 26.0], [22.9, 26.0], [23.0, 26.0], [23.1, 26.0], [23.2, 26.0], [23.3, 26.0], [23.4, 26.0], [23.5, 26.0], [23.6, 26.0], [23.7, 26.0], [23.8, 26.0], [23.9, 26.0], [24.0, 26.0], [24.1, 26.0], [24.2, 26.0], [24.3, 26.0], [24.4, 26.0], [24.5, 26.0], [24.6, 26.0], [24.7, 26.0], [24.8, 26.0], [24.9, 26.0], [25.0, 26.0], [25.1, 26.0], [25.2, 26.0], [25.3, 26.0], [25.4, 26.0], [25.5, 26.0], [25.6, 26.0], [25.7, 26.0], [25.8, 27.0], [25.9, 27.0], [26.0, 27.0], [26.1, 27.0], [26.2, 27.0], [26.3, 27.0], [26.4, 27.0], [26.5, 27.0], [26.6, 27.0], [26.7, 27.0], [26.8, 27.0], [26.9, 27.0], [27.0, 27.0], [27.1, 27.0], [27.2, 27.0], [27.3, 27.0], [27.4, 27.0], [27.5, 27.0], [27.6, 27.0], [27.7, 27.0], [27.8, 27.0], [27.9, 27.0], [28.0, 27.0], [28.1, 27.0], [28.2, 27.0], [28.3, 27.0], [28.4, 27.0], [28.5, 27.0], [28.6, 27.0], [28.7, 27.0], [28.8, 27.0], [28.9, 27.0], [29.0, 27.0], [29.1, 27.0], [29.2, 27.0], [29.3, 27.0], [29.4, 27.0], [29.5, 27.0], [29.6, 27.0], [29.7, 27.0], [29.8, 27.0], [29.9, 27.0], [30.0, 27.0], [30.1, 27.0], [30.2, 27.0], [30.3, 27.0], [30.4, 27.0], [30.5, 27.0], [30.6, 27.0], [30.7, 27.0], [30.8, 27.0], [30.9, 27.0], [31.0, 27.0], [31.1, 27.0], [31.2, 27.0], [31.3, 27.0], [31.4, 27.0], [31.5, 27.0], [31.6, 27.0], [31.7, 27.0], [31.8, 27.0], [31.9, 27.0], [32.0, 27.0], [32.1, 27.0], [32.2, 27.0], [32.3, 27.0], [32.4, 27.0], [32.5, 27.0], [32.6, 27.0], [32.7, 27.0], [32.8, 27.0], [32.9, 27.0], [33.0, 27.0], [33.1, 27.0], [33.2, 27.0], [33.3, 27.0], [33.4, 27.0], [33.5, 27.0], [33.6, 27.0], [33.7, 27.0], [33.8, 27.0], [33.9, 27.0], [34.0, 27.0], [34.1, 28.0], [34.2, 28.0], [34.3, 28.0], [34.4, 28.0], [34.5, 28.0], [34.6, 28.0], [34.7, 28.0], [34.8, 28.0], [34.9, 28.0], [35.0, 28.0], [35.1, 28.0], [35.2, 28.0], [35.3, 28.0], [35.4, 28.0], [35.5, 28.0], [35.6, 28.0], [35.7, 28.0], [35.8, 28.0], [35.9, 28.0], [36.0, 28.0], [36.1, 28.0], [36.2, 28.0], [36.3, 28.0], [36.4, 28.0], [36.5, 28.0], [36.6, 28.0], [36.7, 28.0], [36.8, 28.0], [36.9, 28.0], [37.0, 28.0], [37.1, 28.0], [37.2, 28.0], [37.3, 28.0], [37.4, 28.0], [37.5, 28.0], [37.6, 28.0], [37.7, 28.0], [37.8, 28.0], [37.9, 28.0], [38.0, 28.0], [38.1, 28.0], [38.2, 28.0], [38.3, 28.0], [38.4, 28.0], [38.5, 28.0], [38.6, 28.0], [38.7, 28.0], [38.8, 28.0], [38.9, 28.0], [39.0, 28.0], [39.1, 28.0], [39.2, 28.0], [39.3, 28.0], [39.4, 28.0], [39.5, 28.0], [39.6, 28.0], [39.7, 28.0], [39.8, 28.0], [39.9, 28.0], [40.0, 28.0], [40.1, 28.0], [40.2, 28.0], [40.3, 28.0], [40.4, 28.0], [40.5, 28.0], [40.6, 28.0], [40.7, 28.0], [40.8, 28.0], [40.9, 28.0], [41.0, 28.0], [41.1, 29.0], [41.2, 29.0], [41.3, 29.0], [41.4, 29.0], [41.5, 29.0], [41.6, 29.0], [41.7, 29.0], [41.8, 29.0], [41.9, 29.0], [42.0, 29.0], [42.1, 29.0], [42.2, 29.0], [42.3, 29.0], [42.4, 29.0], [42.5, 29.0], [42.6, 29.0], [42.7, 29.0], [42.8, 29.0], [42.9, 29.0], [43.0, 29.0], [43.1, 29.0], [43.2, 29.0], [43.3, 29.0], [43.4, 29.0], [43.5, 29.0], [43.6, 29.0], [43.7, 29.0], [43.8, 29.0], [43.9, 29.0], [44.0, 29.0], [44.1, 29.0], [44.2, 29.0], [44.3, 29.0], [44.4, 29.0], [44.5, 29.0], [44.6, 29.0], [44.7, 29.0], [44.8, 29.0], [44.9, 29.0], [45.0, 29.0], [45.1, 29.0], [45.2, 29.0], [45.3, 29.0], [45.4, 29.0], [45.5, 29.0], [45.6, 29.0], [45.7, 29.0], [45.8, 29.0], [45.9, 29.0], [46.0, 29.0], [46.1, 29.0], [46.2, 29.0], [46.3, 29.0], [46.4, 29.0], [46.5, 29.0], [46.6, 29.0], [46.7, 29.0], [46.8, 29.0], [46.9, 29.0], [47.0, 30.0], [47.1, 30.0], [47.2, 30.0], [47.3, 30.0], [47.4, 30.0], [47.5, 30.0], [47.6, 30.0], [47.7, 30.0], [47.8, 30.0], [47.9, 30.0], [48.0, 30.0], [48.1, 30.0], [48.2, 30.0], [48.3, 30.0], [48.4, 30.0], [48.5, 30.0], [48.6, 30.0], [48.7, 30.0], [48.8, 30.0], [48.9, 30.0], [49.0, 30.0], [49.1, 30.0], [49.2, 30.0], [49.3, 30.0], [49.4, 30.0], [49.5, 30.0], [49.6, 30.0], [49.7, 30.0], [49.8, 30.0], [49.9, 30.0], [50.0, 30.0], [50.1, 30.0], [50.2, 30.0], [50.3, 30.0], [50.4, 30.0], [50.5, 30.0], [50.6, 30.0], [50.7, 30.0], [50.8, 30.0], [50.9, 30.0], [51.0, 30.0], [51.1, 30.0], [51.2, 30.0], [51.3, 30.0], [51.4, 30.0], [51.5, 30.0], [51.6, 30.0], [51.7, 30.0], [51.8, 30.0], [51.9, 30.0], [52.0, 30.0], [52.1, 30.0], [52.2, 30.0], [52.3, 30.0], [52.4, 30.0], [52.5, 30.0], [52.6, 30.0], [52.7, 30.0], [52.8, 30.0], [52.9, 30.0], [53.0, 30.0], [53.1, 30.0], [53.2, 30.0], [53.3, 30.0], [53.4, 30.0], [53.5, 30.0], [53.6, 30.0], [53.7, 31.0], [53.8, 31.0], [53.9, 31.0], [54.0, 31.0], [54.1, 31.0], [54.2, 31.0], [54.3, 31.0], [54.4, 31.0], [54.5, 31.0], [54.6, 31.0], [54.7, 31.0], [54.8, 31.0], [54.9, 31.0], [55.0, 31.0], [55.1, 31.0], [55.2, 31.0], [55.3, 31.0], [55.4, 31.0], [55.5, 31.0], [55.6, 31.0], [55.7, 31.0], [55.8, 31.0], [55.9, 31.0], [56.0, 31.0], [56.1, 31.0], [56.2, 31.0], [56.3, 31.0], [56.4, 31.0], [56.5, 31.0], [56.6, 31.0], [56.7, 31.0], [56.8, 31.0], [56.9, 31.0], [57.0, 31.0], [57.1, 31.0], [57.2, 31.0], [57.3, 31.0], [57.4, 31.0], [57.5, 31.0], [57.6, 31.0], [57.7, 31.0], [57.8, 31.0], [57.9, 31.0], [58.0, 31.0], [58.1, 31.0], [58.2, 31.0], [58.3, 31.0], [58.4, 31.0], [58.5, 31.0], [58.6, 31.0], [58.7, 31.0], [58.8, 31.0], [58.9, 31.0], [59.0, 31.0], [59.1, 31.0], [59.2, 31.0], [59.3, 31.0], [59.4, 31.0], [59.5, 31.0], [59.6, 31.0], [59.7, 31.0], [59.8, 31.0], [59.9, 31.0], [60.0, 31.0], [60.1, 31.0], [60.2, 31.0], [60.3, 31.0], [60.4, 31.0], [60.5, 31.0], [60.6, 31.0], [60.7, 31.0], [60.8, 31.0], [60.9, 31.0], [61.0, 32.0], [61.1, 32.0], [61.2, 32.0], [61.3, 32.0], [61.4, 32.0], [61.5, 32.0], [61.6, 32.0], [61.7, 32.0], [61.8, 32.0], [61.9, 32.0], [62.0, 32.0], [62.1, 32.0], [62.2, 32.0], [62.3, 32.0], [62.4, 32.0], [62.5, 32.0], [62.6, 32.0], [62.7, 32.0], [62.8, 32.0], [62.9, 32.0], [63.0, 32.0], [63.1, 32.0], [63.2, 32.0], [63.3, 32.0], [63.4, 32.0], [63.5, 32.0], [63.6, 32.0], [63.7, 32.0], [63.8, 32.0], [63.9, 32.0], [64.0, 32.0], [64.1, 32.0], [64.2, 32.0], [64.3, 32.0], [64.4, 32.0], [64.5, 32.0], [64.6, 32.0], [64.7, 32.0], [64.8, 32.0], [64.9, 32.0], [65.0, 32.0], [65.1, 32.0], [65.2, 32.0], [65.3, 32.0], [65.4, 32.0], [65.5, 32.0], [65.6, 32.0], [65.7, 32.0], [65.8, 32.0], [65.9, 32.0], [66.0, 32.0], [66.1, 32.0], [66.2, 32.0], [66.3, 32.0], [66.4, 33.0], [66.5, 33.0], [66.6, 33.0], [66.7, 33.0], [66.8, 33.0], [66.9, 33.0], [67.0, 33.0], [67.1, 33.0], [67.2, 33.0], [67.3, 33.0], [67.4, 33.0], [67.5, 33.0], [67.6, 33.0], [67.7, 33.0], [67.8, 33.0], [67.9, 33.0], [68.0, 33.0], [68.1, 33.0], [68.2, 33.0], [68.3, 33.0], [68.4, 33.0], [68.5, 33.0], [68.6, 33.0], [68.7, 33.0], [68.8, 33.0], [68.9, 33.0], [69.0, 33.0], [69.1, 33.0], [69.2, 33.0], [69.3, 33.0], [69.4, 33.0], [69.5, 33.0], [69.6, 33.0], [69.7, 33.0], [69.8, 33.0], [69.9, 33.0], [70.0, 33.0], [70.1, 33.0], [70.2, 33.0], [70.3, 33.0], [70.4, 33.0], [70.5, 33.0], [70.6, 33.0], [70.7, 33.0], [70.8, 33.0], [70.9, 33.0], [71.0, 33.0], [71.1, 33.0], [71.2, 33.0], [71.3, 33.0], [71.4, 33.0], [71.5, 33.0], [71.6, 33.0], [71.7, 34.0], [71.8, 34.0], [71.9, 34.0], [72.0, 34.0], [72.1, 34.0], [72.2, 34.0], [72.3, 34.0], [72.4, 34.0], [72.5, 34.0], [72.6, 34.0], [72.7, 34.0], [72.8, 34.0], [72.9, 34.0], [73.0, 34.0], [73.1, 34.0], [73.2, 34.0], [73.3, 34.0], [73.4, 34.0], [73.5, 34.0], [73.6, 34.0], [73.7, 34.0], [73.8, 34.0], [73.9, 34.0], [74.0, 34.0], [74.1, 34.0], [74.2, 34.0], [74.3, 34.0], [74.4, 34.0], [74.5, 34.0], [74.6, 34.0], [74.7, 34.0], [74.8, 34.0], [74.9, 34.0], [75.0, 34.0], [75.1, 34.0], [75.2, 34.0], [75.3, 34.0], [75.4, 34.0], [75.5, 34.0], [75.6, 34.0], [75.7, 34.0], [75.8, 34.0], [75.9, 34.0], [76.0, 34.0], [76.1, 34.0], [76.2, 35.0], [76.3, 35.0], [76.4, 35.0], [76.5, 35.0], [76.6, 35.0], [76.7, 35.0], [76.8, 35.0], [76.9, 35.0], [77.0, 35.0], [77.1, 35.0], [77.2, 35.0], [77.3, 35.0], [77.4, 35.0], [77.5, 35.0], [77.6, 35.0], [77.7, 35.0], [77.8, 35.0], [77.9, 35.0], [78.0, 35.0], [78.1, 35.0], [78.2, 35.0], [78.3, 35.0], [78.4, 35.0], [78.5, 35.0], [78.6, 35.0], [78.7, 36.0], [78.8, 36.0], [78.9, 36.0], [79.0, 36.0], [79.1, 36.0], [79.2, 36.0], [79.3, 36.0], [79.4, 36.0], [79.5, 36.0], [79.6, 36.0], [79.7, 36.0], [79.8, 36.0], [79.9, 36.0], [80.0, 36.0], [80.1, 36.0], [80.2, 36.0], [80.3, 36.0], [80.4, 36.0], [80.5, 36.0], [80.6, 36.0], [80.7, 36.0], [80.8, 36.0], [80.9, 36.0], [81.0, 36.0], [81.1, 36.0], [81.2, 36.0], [81.3, 36.0], [81.4, 37.0], [81.5, 37.0], [81.6, 37.0], [81.7, 37.0], [81.8, 37.0], [81.9, 37.0], [82.0, 37.0], [82.1, 37.0], [82.2, 37.0], [82.3, 37.0], [82.4, 37.0], [82.5, 37.0], [82.6, 37.0], [82.7, 37.0], [82.8, 37.0], [82.9, 37.0], [83.0, 37.0], [83.1, 37.0], [83.2, 37.0], [83.3, 37.0], [83.4, 37.0], [83.5, 37.0], [83.6, 37.0], [83.7, 37.0], [83.8, 37.0], [83.9, 37.0], [84.0, 37.0], [84.1, 37.0], [84.2, 37.0], [84.3, 37.0], [84.4, 37.0], [84.5, 38.0], [84.6, 38.0], [84.7, 38.0], [84.8, 38.0], [84.9, 38.0], [85.0, 38.0], [85.1, 38.0], [85.2, 38.0], [85.3, 38.0], [85.4, 38.0], [85.5, 38.0], [85.6, 38.0], [85.7, 38.0], [85.8, 38.0], [85.9, 38.0], [86.0, 38.0], [86.1, 38.0], [86.2, 38.0], [86.3, 39.0], [86.4, 39.0], [86.5, 39.0], [86.6, 39.0], [86.7, 39.0], [86.8, 39.0], [86.9, 39.0], [87.0, 39.0], [87.1, 40.0], [87.2, 40.0], [87.3, 41.0], [87.4, 41.0], [87.5, 41.0], [87.6, 41.0], [87.7, 42.0], [87.8, 42.0], [87.9, 43.0], [88.0, 45.0], [88.1, 58.0], [88.2, 62.0], [88.3, 65.0], [88.4, 79.0], [88.5, 86.0], [88.6, 97.0], [88.7, 97.0], [88.8, 101.0], [88.9, 102.0], [89.0, 102.0], [89.1, 103.0], [89.2, 107.0], [89.3, 117.0], [89.4, 129.0], [89.5, 133.0], [89.6, 137.0], [89.7, 144.0], [89.8, 155.0], [89.9, 162.0], [90.0, 162.0], [90.1, 173.0], [90.2, 180.0], [90.3, 191.0], [90.4, 191.0], [90.5, 206.0], [90.6, 217.0], [90.7, 238.0], [90.8, 241.0], [90.9, 249.0], [91.0, 253.0], [91.1, 279.0], [91.2, 283.0], [91.3, 297.0], [91.4, 310.0], [91.5, 317.0], [91.6, 336.0], [91.7, 341.0], [91.8, 347.0], [91.9, 359.0], [92.0, 373.0], [92.1, 377.0], [92.2, 382.0], [92.3, 391.0], [92.4, 400.0], [92.5, 407.0], [92.6, 434.0], [92.7, 439.0], [92.8, 450.0], [92.9, 450.0], [93.0, 471.0], [93.1, 472.0], [93.2, 473.0], [93.3, 481.0], [93.4, 492.0], [93.5, 517.0], [93.6, 522.0], [93.7, 539.0], [93.8, 543.0], [93.9, 549.0], [94.0, 569.0], [94.1, 576.0], [94.2, 580.0], [94.3, 594.0], [94.4, 600.0], [94.5, 630.0], [94.6, 633.0], [94.7, 636.0], [94.8, 642.0], [94.9, 646.0], [95.0, 665.0], [95.1, 670.0], [95.2, 679.0], [95.3, 695.0], [95.4, 710.0], [95.5, 717.0], [95.6, 719.0], [95.7, 735.0], [95.8, 745.0], [95.9, 755.0], [96.0, 758.0], [96.1, 769.0], [96.2, 774.0], [96.3, 790.0], [96.4, 790.0], [96.5, 791.0], [96.6, 797.0], [96.7, 798.0], [96.8, 804.0], [96.9, 804.0], [97.0, 807.0], [97.1, 808.0], [97.2, 808.0], [97.3, 809.0], [97.4, 810.0], [97.5, 811.0], [97.6, 811.0], [97.7, 812.0], [97.8, 814.0], [97.9, 815.0], [98.0, 815.0], [98.1, 815.0], [98.2, 815.0], [98.3, 816.0], [98.4, 817.0], [98.5, 818.0], [98.6, 818.0], [98.7, 819.0], [98.8, 819.0], [98.9, 820.0], [99.0, 821.0], [99.1, 821.0], [99.2, 821.0], [99.3, 822.0], [99.4, 828.0], [99.5, 828.0], [99.6, 867.0], [99.7, 868.0], [99.8, 868.0], [99.9, 875.0]], "isOverall": false, "label": "MQTT Connect", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 9.0, "minX": 0.0, "maxY": 1000.0, "series": [{"data": [[0.0, 1000.0]], "isOverall": false, "label": "MQTT Disconnect", "isController": false}, {"data": [[0.0, 888.0], [300.0, 10.0], [600.0, 10.0], [700.0, 14.0], [400.0, 11.0], [200.0, 9.0], [100.0, 17.0], [800.0, 32.0], [500.0, 9.0]], "isOverall": false, "label": "MQTT Connect", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 800.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 65.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 1,500ms"], [2, "Requests having \nresponse time > 1,500ms"], [3, "Requests in error"]], "maxY": 1935.0, "series": [{"data": [[0.0, 1935.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [[1.0, 65.0]], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 1,500ms", "isController": false}, {"data": [], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 1,500ms", "isController": false}, {"data": [], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 1.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 4.848760330578512, "minX": 1.7526456E12, "maxY": 29.98354430379744, "series": [{"data": [[1.75264566E12, 4.848760330578512], [1.7526456E12, 29.98354430379744]], "isOverall": false, "label": "Connection Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75264566E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 12.666666666666668, "minX": 1.0, "maxY": 875.0, "series": [{"data": [[2.0, 15.0], [3.0, 14.214285714285715], [4.0, 13.111675126903542], [5.0, 14.610752688172035], [6.0, 16.21768707482993], [7.0, 14.700000000000001], [8.0, 13.050000000000004], [9.0, 12.666666666666668], [10.0, 16.5], [11.0, 13.0], [12.0, 41.0], [14.0, 60.0], [15.0, 30.0], [16.0, 82.0], [17.0, 27.0], [18.0, 74.0], [21.0, 59.333333333333336], [23.0, 27.5], [24.0, 62.5], [27.0, 71.33333333333333], [29.0, 20.0], [30.0, 91.0], [33.0, 90.0], [32.0, 90.0], [35.0, 71.5], [37.0, 86.0], [36.0, 88.0], [41.0, 85.0], [40.0, 86.33333333333333], [45.0, 87.0], [47.0, 86.0], [48.0, 84.0], [52.0, 66.0], [55.0, 75.0], [54.0, 66.0], [56.0, 83.0], [58.0, 80.16666666666667], [59.0, 81.0], [65.0, 72.0], [69.0, 70.25], [68.0, 73.0], [75.0, 72.25], [74.0, 71.0], [72.0, 61.0], [78.0, 59.0], [77.0, 56.0], [76.0, 71.0], [86.0, 47.0], [85.0, 48.0], [84.0, 49.0], [96.0, 38.0], [103.0, 35.0], [101.0, 36.6], [100.0, 37.0], [104.0, 33.0], [108.0, 28.750000000000004], [115.0, 15.0], [113.0, 19.8], [112.0, 22.0], [118.0, 22.5], [116.0, 14.0], [1.0, 13.0]], "isOverall": false, "label": "MQTT Disconnect", "isController": false}, {"data": [[12.423000000000009, 19.183000000000007]], "isOverall": false, "label": "MQTT Disconnect-Aggregated", "isController": false}, {"data": [[8.0, 29.2], [2.0, 26.0], [9.0, 28.2], [10.0, 57.75], [11.0, 56.666666666666664], [12.0, 61.5], [3.0, 27.063829787234038], [14.0, 73.5], [4.0, 28.896464646464626], [78.0, 875.0], [77.0, 117.8], [5.0, 29.044871794871792], [85.0, 613.2], [6.0, 30.403225806451612], [114.0, 474.45454545454544], [113.0, 519.5], [115.0, 611.25], [7.0, 30.354838709677423], [116.0, 547.392156862745], [117.0, 565.2631578947369], [118.0, 42.0]], "isOverall": false, "label": "MQTT Connect", "isController": false}, {"data": [[17.131, 87.73700000000001]], "isOverall": false, "label": "MQTT Connect-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 118.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 0.0, "minX": 1.7526456E12, "maxY": 221.83333333333334, "series": [{"data": [[1.75264566E12, 221.83333333333334], [1.7526456E12, 144.83333333333334]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.75264566E12, 0.0], [1.7526456E12, 0.0]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75264566E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 14.537190082644631, "minX": 1.7526456E12, "maxY": 177.71139240506326, "series": [{"data": [[1.75264566E12, 14.537190082644631], [1.7526456E12, 26.298734177215195]], "isOverall": false, "label": "MQTT Disconnect", "isController": false}, {"data": [[1.75264566E12, 28.993388429752056], [1.7526456E12, 177.71139240506326]], "isOverall": false, "label": "MQTT Connect", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75264566E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.7526456E12, "maxY": 4.9E-324, "series": [{"data": [[1.75264566E12, 0.0], [1.7526456E12, 0.0]], "isOverall": false, "label": "MQTT Disconnect", "isController": false}, {"data": [[1.75264566E12, 0.0], [1.7526456E12, 0.0]], "isOverall": false, "label": "MQTT Connect", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75264566E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.7526456E12, "maxY": 4.9E-324, "series": [{"data": [[1.75264566E12, 0.0], [1.7526456E12, 0.0]], "isOverall": false, "label": "MQTT Disconnect", "isController": false}, {"data": [[1.75264566E12, 0.0], [1.7526456E12, 0.0]], "isOverall": false, "label": "MQTT Connect", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75264566E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 8.0, "minX": 1.7526456E12, "maxY": 875.0, "series": [{"data": [[1.75264566E12, 41.0], [1.7526456E12, 875.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.75264566E12, 33.0], [1.7526456E12, 376.5999999999999]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.75264566E12, 38.8900000000001], [1.7526456E12, 821.09]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.75264566E12, 36.0], [1.7526456E12, 762.9499999999992]], "isOverall": false, "label": "95th percentile", "isController": false}, {"data": [[1.75264566E12, 8.0], [1.7526456E12, 8.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.75264566E12, 19.0], [1.7526456E12, 27.0]], "isOverall": false, "label": "Median", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75264566E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 19.0, "minX": 12.0, "maxY": 38.0, "series": [{"data": [[387.0, 38.0], [198.0, 19.0], [199.0, 19.0], [12.0, 20.0], [202.0, 19.0], [201.0, 19.0], [200.0, 20.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 387.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 12.0, "maxY": 4.9E-324, "series": [{"data": [[387.0, 0.0], [198.0, 0.0], [199.0, 0.0], [12.0, 0.0], [202.0, 0.0], [201.0, 0.0], [200.0, 0.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 387.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 13.216666666666667, "minX": 1.7526456E12, "maxY": 20.116666666666667, "series": [{"data": [[1.75264566E12, 20.116666666666667], [1.7526456E12, 13.216666666666667]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75264566E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 13.166666666666666, "minX": 1.7526456E12, "maxY": 20.166666666666668, "series": [{"data": [[1.75264566E12, 20.166666666666668], [1.7526456E12, 13.166666666666666]], "isOverall": false, "label": "200", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.75264566E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 6.583333333333333, "minX": 1.7526456E12, "maxY": 10.083333333333334, "series": [{"data": [[1.75264566E12, 10.083333333333334], [1.7526456E12, 6.583333333333333]], "isOverall": false, "label": "MQTT Connect-success", "isController": false}, {"data": [[1.75264566E12, 10.083333333333334], [1.7526456E12, 6.583333333333333]], "isOverall": false, "label": "MQTT Disconnect-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75264566E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 13.166666666666666, "minX": 1.7526456E12, "maxY": 20.166666666666668, "series": [{"data": [[1.75264566E12, 20.166666666666668], [1.7526456E12, 13.166666666666666]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.75264566E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 28800000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

