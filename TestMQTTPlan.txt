<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="MQTT Load Test Plan" enabled="true">
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments">
        <collectionProp name="Arguments.arguments">
          <elementProp name="server" elementType="Argument">
            <stringProp name="Argument.name">server</stringProp>
            <stringProp name="Argument.value">10.211.55.5</stringProp>
          </elementProp>
          <elementProp name="port" elementType="Argument">
            <stringProp name="Argument.name">port</stringProp>
            <stringProp name="Argument.value">1883</stringProp>
          </elementProp>
          <elementProp name="loopCount" elementType="Argument">
            <stringProp name="Argument.name">loopCount</stringProp>
            <stringProp name="Argument.value">100</stringProp>
          </elementProp>
          <elementProp name="publishInterval" elementType="Argument">
            <stringProp name="Argument.name">publishInterval</stringProp>
            <stringProp name="Argument.value">100</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>

      <!-- Thread Group: Connection Group -->
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Connection Group" enabled="true">
        <stringProp name="ThreadGroup.num_threads">10</stringProp>
        <stringProp name="ThreadGroup.ramp_time">5</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">1</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
      </ThreadGroup>
      <hashTree>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect" enabled="true">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.client_id_prefix">conn_</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.version">3.1</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect" enabled="true"/>
        <hashTree/>
      </hashTree>

      <!-- Thread Group: Publish Group -->
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Publish Group" enabled="true">
        <stringProp name="ThreadGroup.num_threads">50</stringProp>
        <stringProp name="ThreadGroup.ramp_time">10</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">${loopCount}</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
      </ThreadGroup>
      <hashTree>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect" enabled="true">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.client_id_prefix">pub_</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.version">3.1</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>

        <net.xmeter.samplers.PubSampler guiclass="net.xmeter.gui.PubSamplerUI" testclass="net.xmeter.samplers.PubSampler" testname="MQTT Publish" enabled="true">
          <stringProp name="mqtt.topic_name">test/topic/${__threadNum}</stringProp>
          <stringProp name="mqtt.payload">{"value":${__Random(1,100)}}</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.retained">false</boolProp>
        </net.xmeter.samplers.PubSampler>
        <hashTree/>

        <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="Publish Interval Timer" enabled="true">
          <stringProp name="ConstantTimer.delay">${publishInterval}</stringProp>
        </ConstantTimer>
        <hashTree/>

        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect" enabled="true"/>
        <hashTree/>
      </hashTree>

      <!-- Listeners -->
      <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <stringProp name="filename">mqtt_results_summary.jtl</stringProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time><latency>true</latency><timestamp>true</timestamp>
            <success>true</success><label>true</label><code>true</code><message>true</message>
            <threadName>true</threadName><assertions>true</assertions>
            <subresults>true</subresults><bytes>true</bytes><sentBytes>true</sentBytes>
          </value>
        </objProp>
      </ResultCollector>
      <hashTree/>

      <ResultCollector guiclass="AggregateReport" testclass="ResultCollector" testname="Aggregate Report" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <stringProp name="filename">mqtt_results_aggregate.jtl</stringProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time><latency>true</latency><timestamp>true</timestamp>
            <success>true</success><label>true</label><code>true</code><message>true</message>
            <threadName>true</threadName><assertions>true</assertions>
            <subresults>true</subresults><bytes>true</bytes><sentBytes>true</sentBytes>
          </value>
        </objProp>
      </ResultCollector>
      <hashTree/>

    </hashTree>
  </hashTree>
</jmeterTestPlan>