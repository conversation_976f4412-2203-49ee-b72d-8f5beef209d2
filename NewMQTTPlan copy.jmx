<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="MQTT性能测试计划">
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
    </TestPlan>
    <hashTree>
      <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="xmeter_runtime_vars">
        <collectionProp name="Arguments.arguments">
          <elementProp name="server" elementType="Argument">
            <stringProp name="Argument.name">server</stringProp>
            <stringProp name="Argument.value">47.99.182.252</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">MQTT Broker地址</stringProp>
          </elementProp>
          <elementProp name="port" elementType="Argument">
            <stringProp name="Argument.name">port</stringProp>
            <stringProp name="Argument.value">1883</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">MQTT端口</stringProp>
          </elementProp>
          <elementProp name="clientIdPrefix" elementType="Argument">
            <stringProp name="Argument.name">clientIdPrefix</stringProp>
            <stringProp name="Argument.value">mqtt_test</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">客户端ID前缀</stringProp>
          </elementProp>
          <elementProp name="connKeepAlive" elementType="Argument">
            <stringProp name="Argument.name">connKeepAlive</stringProp>
            <stringProp name="Argument.value">60</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">连接保活时间(秒)</stringProp>
          </elementProp>
          <elementProp name="threadCount" elementType="Argument">
            <stringProp name="Argument.name">threadCount</stringProp>
            <stringProp name="Argument.value">1000</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">并发客户端数量</stringProp>
          </elementProp>
          <elementProp name="rampUp" elementType="Argument">
            <stringProp name="Argument.name">rampUp</stringProp>
            <stringProp name="Argument.value">10</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">并发启动时长(秒)</stringProp>
          </elementProp>
          <elementProp name="loopCount" elementType="Argument">
            <stringProp name="Argument.name">loopCount</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">每个线程循环发布消息次数</stringProp>
          </elementProp>
          <elementProp name="publishInterval" elementType="Argument">
            <stringProp name="Argument.name">publishInterval</stringProp>
            <stringProp name="Argument.value">100</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">发布消息间隔(毫秒)</stringProp>
          </elementProp>
          <elementProp name="randomImei" elementType="Argument">
            <stringProp name="Argument.name">randomImei</stringProp>
            <stringProp name="Argument.value">${__Random(0,999999)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机IMEI值</stringProp>
          </elementProp>
          <elementProp name="randomHour" elementType="Argument">
            <stringProp name="Argument.name">randomHour</stringProp>
            <stringProp name="Argument.value">${__Random(0,23)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机小时</stringProp>
          </elementProp>
          <elementProp name="randomMinute" elementType="Argument">
            <stringProp name="Argument.name">randomMinute</stringProp>
            <stringProp name="Argument.value">${__Random(0,59)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机分钟</stringProp>
          </elementProp>
          <elementProp name="randomSecond" elementType="Argument">
            <stringProp name="Argument.name">randomSecond</stringProp>
            <stringProp name="Argument.value">${__Random(0,59)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机秒</stringProp>
          </elementProp>
          <elementProp name="randomDay" elementType="Argument">
            <stringProp name="Argument.name">randomDay</stringProp>
            <stringProp name="Argument.value">${__Random(1,31)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机日期</stringProp>
          </elementProp>
          <elementProp name="randomIndex" elementType="Argument">
            <stringProp name="Argument.name">randomIndex</stringProp>
            <stringProp name="Argument.value">${__Random(0,2881)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机index值</stringProp>
          </elementProp>
          <elementProp name="randomLongitude" elementType="Argument">
            <stringProp name="Argument.name">randomLongitude</stringProp>
            <stringProp name="Argument.value">${__Random(1000000,9999999)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机经度值</stringProp>
          </elementProp>
          <elementProp name="randomLatitude" elementType="Argument">
            <stringProp name="Argument.name">randomLatitude</stringProp>
            <stringProp name="Argument.value">${__Random(1000000,9999999)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机纬度值</stringProp>
          </elementProp>
          <elementProp name="randomTtff" elementType="Argument">
            <stringProp name="Argument.name">randomTtff</stringProp>
            <stringProp name="Argument.value">${__Random(1,15)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机TTFF值</stringProp>
          </elementProp>
          <elementProp name="randomAltitude" elementType="Argument">
            <stringProp name="Argument.name">randomAltitude</stringProp>
            <stringProp name="Argument.value">${__Random(0,1000)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机海拔值</stringProp>
          </elementProp>
          <elementProp name="randomSpeed" elementType="Argument">
            <stringProp name="Argument.name">randomSpeed</stringProp>
            <stringProp name="Argument.value">${__Random(0,200)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">随机速度值</stringProp>
          </elementProp>
        </collectionProp>
      </Arguments>
      <hashTree/>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Connection Group">
        <stringProp name="ThreadGroup.num_threads">${threadCount}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${rampUp}</stringProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.version">3.1.1</stringProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
          <stringProp name="mqtt.client_id_prefix">${clientIdPrefix}_conn</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.conn_keep_alive">${connKeepAlive}</stringProp>
          <stringProp name="mqtt.ws_path"></stringProp>
          <boolProp name="mqtt.dual_ssl_authentication">false</boolProp>
          <stringProp name="mqtt.clientcert_file_path"></stringProp>
          <stringProp name="mqtt.clientcert_password"></stringProp>
          <stringProp name="mqtt.user_name"></stringProp>
          <stringProp name="mqtt.password"></stringProp>
          <stringProp name="mqtt.conn_attampt_max">0</stringProp>
          <stringProp name="mqtt.reconn_attampt_max">0</stringProp>
          <stringProp name="mqtt.conn_clean_session">true</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect"/>
        <hashTree/>
      </hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Publish Group" enabled="false">
        <stringProp name="ThreadGroup.num_threads">${threadCount}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${rampUp}</stringProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器">
          <stringProp name="LoopController.loops">${loopCount}</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect" enabled="true">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.version">3.1</stringProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
          <stringProp name="mqtt.client_id_prefix">${clientIdPrefix}_pub</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.conn_keep_alive">${connKeepAlive}</stringProp>
          <stringProp name="mqtt.ws_path"></stringProp>
          <boolProp name="mqtt.dual_ssl_authentication">false</boolProp>
          <stringProp name="mqtt.clientcert_file_path"></stringProp>
          <stringProp name="mqtt.clientcert_password"></stringProp>
          <stringProp name="mqtt.user_name"></stringProp>
          <stringProp name="mqtt.password"></stringProp>
          <stringProp name="mqtt.conn_attampt_max">0</stringProp>
          <stringProp name="mqtt.reconn_attampt_max">0</stringProp>
          <stringProp name="mqtt.conn_clean_session">true</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <net.xmeter.samplers.PubSampler guiclass="net.xmeter.gui.PubSamplerUI" testclass="net.xmeter.samplers.PubSampler" testname="MQTT Publish" enabled="true">
          <stringProp name="mqtt.topic_name">/data-instance</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.add_timestamp">false</boolProp>
          <stringProp name="mqtt.message_type">Hex string</stringProp>
          <stringProp name="mqtt.message_to_sent">27${__RandomString(6,0123456789ABCDEF)}A6DE0202190C${__intToHexString(${__Random(1,31)},1)}${__intToHexString(${__Random(0,23)},1)}${__intToHexString(${__Random(0,59)},1)}${__intToHexString(${__Random(0,59)},1)}50${__intToHexString(${randomIndex},4)}${__intToHexString(${randomLongitude},8)}${__intToHexString(${randomLatitude},8)}${__intToHexString(${randomTtff},2)}0D1807121B${__intToHexString(${randomAltitude},4)}${__intToHexString(${randomSpeed},4)}</stringProp>
          <stringProp name="mqtt.message_type_fixed_length">1024</stringProp>
          <boolProp name="mqtt.retained_message">false</boolProp>
        </net.xmeter.samplers.PubSampler>
        <hashTree/>
        <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="消息发布间隔" enabled="true">
          <stringProp name="ConstantTimer.delay">${publishInterval}</stringProp>
        </ConstantTimer>
        <hashTree/>
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect" enabled="true"/>
        <hashTree/>
      </hashTree>
      <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename">E:\Mqtt-jemter\Logs\summary_report.jtl</stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename">E:\Mqtt-jemter\Logs\results_tree.jtl</stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="Aggregate Report">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename">E:\Mqtt-jemter\Logs\aggregate_report.jtl</stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="GraphVisualizer" testclass="ResultCollector" testname="Response Time Graph">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename">E:\Mqtt-jemter\Logs\response_time_graph.jtl</stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="RespTimeGraphVisualizer" testclass="ResultCollector" testname="Aggregate Graph">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename">E:\Mqtt-jemter\Logs\aggregate_graph.jtl</stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="SimpleDataWriter" testclass="ResultCollector" testname="Save Responses to a file">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>true</responseData>
            <samplerData>true</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>true</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename">E:\Mqtt-jemter\Logs\detailed_results.jtl</stringProp>
      </ResultCollector>
      <hashTree/>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
