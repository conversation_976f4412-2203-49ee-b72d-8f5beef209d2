# MQTT 接入性能测试报告

## 一、测试说明

### 1.1 测试目的
本次测试旨在验证 MQTT 接入服务在模拟大量客户端连接、消息发布、断开过程中的稳定性与响应性能，为系统上线提供性能保障依据。通过高并发场景测试，评估系统在5000并发用户下的处理能力和稳定性表现。

### 1.2 测试范围
**测试对象**：MQTT 接入服务（包括连接、消息发布与断开连接流程）

**测试内容**：接口性能、响应时间、并发稳定性、系统吞吐量

**注意**：本次测试主要验证 MQTT 传输链路性能，重点关注高并发场景下的系统稳定性和响应能力。

### 1.3 测试人员
**测试工程师**：性能测试团队

**项目负责人**：系统架构师

**测试时间**：2025年7月23日

**测试环境**：ConnectPubTest5000-2

## 二、测试方案

### 2.1 测试工具
**工具名称**：Apache JMeter

**说明**：通过 JMeter 的 MQTT 插件，构建连接-发布-断开三类请求，模拟大规模客户端并发行为。使用 HiveMQTT Connection 作为客户端连接器。

### 2.2 测试前置条件
- MQTT 服务部署完成，运行正常
- 测试账号、设备 ID、发布主题等参数配置完成
- MQTT Broker 支持高并发连接，测试期间无重启或宕机记录
- JMeter 测试环境配置完成，性能监控工具就绪

### 2.3 测试配置
| 配置项 | 数值 |
|--------|------|
| 并发线程数 | 5000 |
| 每线程请求数 | 3次（Connect → Publish → Disconnect） |
| 总请求数 | 15000 |
| 测试总时长 | 约 9 秒 |
| 消息负载大小 | 发送：33字节，接收：20字节 |

## 三、测试记录

### 3.1 测试1 - 性能测试

#### 3.1.1 测试配置
| 配置项 | 数值 |
|--------|------|
| 并发线程数 | 5000 |
| 每线程请求数 | 3次（Connect → Publish → Disconnect） |
| 总请求数 | 15000 |
| 测试总时长 | 约 9 秒 |
| 客户端类型 | HiveMQTT Connection |

#### 3.1.2 结果概览（分类型）
| 请求类型 | 总请求数 | 成功数 | 失败数 | 平均响应时间(ms) | 最大响应时间(ms) | 最小响应时间(ms) | 成功率 | 吞吐量(TPS) |
|----------|----------|--------|--------|------------------|------------------|------------------|--------|-------------|
| MQTT Connect | 5000 | 5000 | 0 | 1465 | 4607 | 21 | 100.0% | 581.87 |
| MQTT Publish | 5000 | 5000 | 0 | 5 | 151 | 0 | 100.0% | 941.09 |
| MQTT Disconnect | 5000 | 5000 | 0 | 264 | 1604 | 10 | 100.0% | 950.57 |

#### 3.1.3 详细性能指标

**MQTT 连接性能**
| 指标 | 数值 |
|------|------|
| 样本数量 | 5,000 个连接 |
| 平均响应时间 | 1,465 ms |
| 中位数响应时间 | 1,160 ms |
| 90% 响应时间 | 3,660 ms |
| 95% 响应时间 | 3,906 ms |
| 99% 响应时间 | 4,073 ms |
| 标准差 | 1,411.40 ms |
| 错误率 | 0.000% |
| 吞吐量 | 581.87 连接/秒 |

**MQTT 消息发布性能**
| 指标 | 数值 |
|------|------|
| 样本数量 | 5,000 条消息 |
| 平均响应时间 | 5 ms |
| 中位数响应时间 | 0 ms |
| 90% 响应时间 | 9 ms |
| 95% 响应时间 | 34 ms |
| 99% 响应时间 | 121 ms |
| 标准差 | 19.10 ms |
| 错误率 | 0.000% |
| 吞吐量 | 941.09 消息/秒 |

**MQTT 断开连接性能**
| 指标 | 数值 |
|------|------|
| 样本数量 | 5,000 个断开操作 |
| 平均响应时间 | 264 ms |
| 中位数响应时间 | 79 ms |
| 90% 响应时间 | 808 ms |
| 95% 响应时间 | 890 ms |
| 99% 响应时间 | 1,206 ms |
| 标准差 | 320.48 ms |
| 错误率 | 0.000% |
| 吞吐量 | 950.57 断开/秒 |

#### 3.1.4 系统资源监控

**CPU 使用情况**
- 测试期间 CPU 使用率保持在合理范围内
- 峰值 CPU 使用率出现在连接建立阶段
- 消息发布阶段 CPU 使用率相对平稳
- 整体 CPU 资源利用充分但未出现过载

![CPU占用情况](CPU占用.png)

**内存使用情况**
- 内存使用量随并发连接数增长呈线性上升趋势
- 连接建立阶段内存占用快速增长
- 消息发布阶段内存使用相对稳定
- 连接断开后内存得到及时释放，无明显内存泄漏

![内存占用情况](内存占用.png)

**系统负载情况**
- 系统整体负载在测试期间保持稳定
- 负载峰值出现在高并发连接建立阶段
- 系统响应能力良好，无阻塞或超时现象
- 负载分布均匀，系统资源调度合理

![系统负载情况](系统负载情况.png)

**资源使用分析**
| 资源类型 | 峰值使用率 | 平均使用率 | 资源状态 | 备注 |
|----------|------------|------------|----------|------|
| CPU | 约85% | 约60% | 良好 | 连接建立阶段负载较高 |
| 内存 | 约75% | 约45% | 良好 | 随连接数线性增长 |
| 系统负载 | 约3.2 | 约2.1 | 正常 | 负载分布均匀 |
| 网络I/O | 中等 | 低 | 良好 | 主要集中在消息传输阶段 |

#### 3.1.5 测试日志摘要
- 所有请求返回响应码 200
- MQTT 报文成功被 Broker 接收和处理
- 并发线程稳定，没有线程阻塞或异常退出
- 服务器在高并发场景下表现稳定，无明显性能瓶颈
- 连接建立过程中存在排队现象，但最终全部成功建立
- 系统资源使用合理，CPU、内存、负载均在可控范围内

## 四、测试总结

### 4.1 测试结果概述

| 指标项 | 数值 |
|--------|------|
| 测试总请求数 | 15,000 |
| 成功请求数 | 15,000 |
| 失败请求数 | 0 |
| 请求成功率 | 100.00% |
| 测试起始时间 | 2025-07-23 16:46:12 |
| 测试结束时间 | 2025-07-23 16:46:21 |
| 测试持续时间 | 约 9 秒 |
| 平均响应时间 | 578 ms |
| 总吞吐量 | 1,693.00 操作/秒 |
| 接收数据速率 | 23.15 KB/秒 |
| 发送数据速率 | 18.19 KB/秒 |

### 4.2 测试结论

#### ✅ 优点：
1. **MQTT 三类请求全部成功，服务稳定可靠**
   - 15,000个请求100%成功，无任何失败案例
   - 系统在5000并发用户场景下表现稳定

2. **消息发布性能优异，具备大规模设备并发接入的能力**
   - 消息发布平均响应时间仅5ms，实时性极佳
   - 发布吞吐量达到941.09消息/秒，满足高并发需求

3. **测试覆盖典型使用流程（连接、发布、断开）**
   - 完整验证了MQTT客户端生命周期的各个阶段
   - 各阶段性能指标均在可接受范围内

#### ⚠️ 不足/问题：
1. **连接建立阶段存在性能瓶颈**
   - 平均连接时间1.465秒，在高并发场景下排队等待明显
   - 连接建立阶段响应时间波动较大（标准差1411.40ms）

2. **当前测试未涵盖完整业务流程验证**
   - 主要验证传输链路性能，未深入测试消息解析与处理逻辑
   - 缺少对QoS保障机制、Retain消息等MQTT特性的验证

3. **系统资源使用分析**
   - CPU峰值使用率约85%，主要集中在连接建立阶段
   - 内存使用随连接数线性增长，峰值约75%，资源释放及时
   - 系统负载峰值约3.2，整体负载分布均匀且可控

#### 📌 后续建议：
1. **连接性能优化**
   - 优化连接池配置，提高连接建立效率，减少CPU峰值负载
   - 考虑部署负载均衡，分散高并发连接压力
   - 基于当前85%的CPU峰值使用率，建议预留15-20%的资源缓冲

2. **资源管理优化**
   - 基于内存使用线性增长特性，建议设置合理的连接数上限
   - 优化内存管理策略，确保连接断开后资源及时释放
   - 监控系统负载趋势，建立负载预警机制

3. **完善测试覆盖**
   - 开展链路压测，验证MQTT报文完整解析、存储与反馈流程
   - 基于当前资源监控数据，进行更大规模的并发测试
   - 测试QoS保障机制、Retain消息等关键MQTT特性功能表现

4. **环境适应性测试**
   - 模拟网络抖动、延迟等环境，验证弱网适应性
   - 构建业务用例压测（如：真实设备行为、断线重连、周期性上报）
   - 进行长时间稳定性测试，验证系统持续运行能力和资源使用趋势

## 五、附件

### 5.1 测试数据文件
| 文件名 | 说明 | 用途 |
|--------|------|------|
| publish_detailed.jtl | 详细测试数据记录 | 包含每个请求的完整执行信息 |
| aggregate.csv | 聚合测试报告 | 按请求类型汇总的性能指标 |
| summary.csv | 汇总统计数据 | 整体测试结果统计摘要 |
| publish_aggregate.jtl | 聚合数据源文件 | JMeter聚合监听器原始数据 |
| results_tree.jtl | 结果树数据 | 请求响应详细信息 |

### 5.2 测试配置与图表
- **JMeter 脚本配置**：ConnectPubTest5000-2 测试计划
- **性能图表**：Publish Aggregate Graph.png
- **测试环境截图**：PixPin_2025-07-23_16-48-13.png
- **JMeter 日志文件**：jmeter5000-2.log

### 5.3 系统资源监控图表
| 图表名称 | 文件名 | 监控内容 |
|----------|--------|----------|
| CPU使用率监控 | CPU占用.png | 测试期间CPU使用率变化趋势 |
| 内存使用监控 | 内存占用.png | 内存占用情况和释放趋势 |
| 系统负载监控 | 系统负载情况.png | 系统整体负载分布情况 |
| 磁盘I/O监控 | 磁盘读写.png | 磁盘读写性能表现 |

### 5.4 关键数据摘要
```
测试执行时间：2025-07-23 16:46:12 - 16:46:21
总执行时长：约9秒
并发用户数：5000
总请求数：15000（Connect: 5000, Publish: 5000, Disconnect: 5000）
成功率：100%
平均TPS：1693.00
```

---
**报告生成时间**：2025年7月23日
**报告版本**：v1.0
**测试环境**：ConnectPubTest5000-2
