<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="3.2" jmeter="3.2 r1790748">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Test Plan" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="xmeter_runtime_vars" enabled="true">
        <collectionProp name="Arguments.arguments">
          <elementProp name="server" elementType="Argument">
            <stringProp name="Argument.name">server</stringProp>
            <stringProp name="Argument.value">10.211.55.5</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="port" elementType="Argument">
            <stringProp name="Argument.name">port</stringProp>
            <stringProp name="Argument.value">1883</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </Arguments>
      <hashTree/>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Device Group" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">1</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">2</stringProp>
        <stringProp name="ThreadGroup.ramp_time">1</stringProp>
        <longProp name="ThreadGroup.start_time">1518079165000</longProp>
        <longProp name="ThreadGroup.end_time">1518079165000</longProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="Dev Connect" enabled="true">
          <stringProp name="mqtt.keystore_file_path"></stringProp>
          <stringProp name="mqtt.clientcert_file_path"></stringProp>
          <stringProp name="mqtt.conn_keep_alive">8</stringProp>
          <stringProp name="mqtt.conn_attempt_max">0</stringProp>
          <stringProp name="mqtt.client_id_prefix">Dev_</stringProp>
          <boolProp name="mqtt.conn_share">false</boolProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.reconn_attempt_max">0</stringProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
          <boolProp name="mqtt.dual_ssl_authentication">false</boolProp>
          <stringProp name="mqtt.keystore_password"></stringProp>
          <stringProp name="mqtt.clientcert_password"></stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.version">3.1</stringProp>
          <stringProp name="mqtt.user_name"></stringProp>
          <stringProp name="mqtt.password"></stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <net.xmeter.samplers.PubSampler guiclass="net.xmeter.gui.PubSamplerUI" testclass="net.xmeter.samplers.PubSampler" testname="Dev Publish" enabled="true">
          <stringProp name="mqtt.topic_name">DevTopic_${__threadNum}</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.add_timestamp">true</boolProp>
          <stringProp name="mqtt.message_type">String</stringProp>
          <stringProp name="mqtt.message_type_fixed_length">1024</stringProp>
          <stringProp name="mqtt.message_to_sent">Message from Device ${__threadNum}.</stringProp>
        </net.xmeter.samplers.PubSampler>
        <hashTree>
          <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="wait 3 seconds before MobSub start" enabled="true">
            <stringProp name="ConstantTimer.delay">3000</stringProp>
          </ConstantTimer>
          <hashTree/>
        </hashTree>
        <net.xmeter.samplers.SubSampler guiclass="net.xmeter.gui.SubSamplerUI" testclass="net.xmeter.samplers.SubSampler" testname="Dev Subscribe" enabled="true">
          <stringProp name="mqtt.topic_name">MobTopic_${__threadNum}</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.add_timestamp">true</boolProp>
          <boolProp name="mqtt.debug_response">true</boolProp>
          <stringProp name="mqtt.sample_condition">number of received messages</stringProp>
          <stringProp name="mqtt.sample_condition_value">1</stringProp>
        </net.xmeter.samplers.SubSampler>
        <hashTree/>
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="Dev DisConnect" enabled="true"/>
        <hashTree/>
        <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="Delay between sampler" enabled="true">
          <stringProp name="ConstantTimer.delay">300</stringProp>
        </ConstantTimer>
        <hashTree/>
      </hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Mobile Group" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">1</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">2</stringProp>
        <stringProp name="ThreadGroup.ramp_time">1</stringProp>
        <longProp name="ThreadGroup.start_time">1518079165000</longProp>
        <longProp name="ThreadGroup.end_time">1518079165000</longProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="Mob Connect" enabled="true">
          <stringProp name="mqtt.keystore_file_path"></stringProp>
          <stringProp name="mqtt.clientcert_file_path"></stringProp>
          <stringProp name="mqtt.conn_keep_alive">8</stringProp>
          <stringProp name="mqtt.conn_attempt_max">0</stringProp>
          <stringProp name="mqtt.client_id_prefix">Mob_</stringProp>
          <boolProp name="mqtt.conn_share">false</boolProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.reconn_attempt_max">0</stringProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
          <boolProp name="mqtt.dual_ssl_authentication">false</boolProp>
          <stringProp name="mqtt.keystore_password"></stringProp>
          <stringProp name="mqtt.clientcert_password"></stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.version">3.1</stringProp>
          <stringProp name="mqtt.user_name"></stringProp>
          <stringProp name="mqtt.password"></stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <net.xmeter.samplers.SubSampler guiclass="net.xmeter.gui.SubSamplerUI" testclass="net.xmeter.samplers.SubSampler" testname="Mob Subscribe" enabled="true">
          <stringProp name="mqtt.topic_name">DevTopic_${__threadNum}</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.add_timestamp">true</boolProp>
          <boolProp name="mqtt.debug_response">true</boolProp>
          <stringProp name="mqtt.sample_condition">number of received messages</stringProp>
          <stringProp name="mqtt.sample_condition_value">1</stringProp>
        </net.xmeter.samplers.SubSampler>
        <hashTree/>
        <net.xmeter.samplers.PubSampler guiclass="net.xmeter.gui.PubSamplerUI" testclass="net.xmeter.samplers.PubSampler" testname="Mob Publish" enabled="true">
          <stringProp name="mqtt.topic_name">MobTopic_${__threadNum}</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.add_timestamp">true</boolProp>
          <stringProp name="mqtt.message_type">String</stringProp>
          <stringProp name="mqtt.message_type_fixed_length">1024</stringProp>
          <stringProp name="mqtt.message_to_sent">Mobile ${__threadNum} send this message.</stringProp>
        </net.xmeter.samplers.PubSampler>
        <hashTree>
          <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="wait 4 seconds before DevSub start" enabled="true">
            <stringProp name="ConstantTimer.delay">4000</stringProp>
          </ConstantTimer>
          <hashTree/>
        </hashTree>
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="Mob DisConnect" enabled="true"/>
        <hashTree/>
        <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="Delay between sampler" enabled="true">
          <stringProp name="ConstantTimer.delay">300</stringProp>
        </ConstantTimer>
        <hashTree/>
      </hashTree>
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="TableVisualizer" testclass="ResultCollector" testname="View Results in Table" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
    </hashTree>
    <WorkBench guiclass="WorkBenchGui" testclass="WorkBench" testname="WorkBench" enabled="true">
      <boolProp name="WorkBench.save">true</boolProp>
    </WorkBench>
    <hashTree/>
  </hashTree>
</jmeterTestPlan>
