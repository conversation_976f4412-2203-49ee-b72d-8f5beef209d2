nohup: ignoring input
19:22:59.095 [main] INFO org.jeecg.JeecgSystemApplication - [JEECG] Elasticsearch Health Check Enabled: false

   (_)                          | |               | |
    _  ___  ___  ___ __ _ ______| |__   ___   ___ | |_ 
   | |/ _ \/ _ \/ __/ _` |______| '_ \ / _ \ / _ \| __|
   | |  __/  __/ (_| (_| |      | |_) | (_) | (_) | |_ 
   | |\___|\___|\___\__, |      |_.__/ \___/ \___/ \__|
  _/ |               __/ |                             
 |__/               |___/



Jeecg  Boot Version: 3.7.3
Spring Boot Version: 2.7.18 (v2.7.18)
产品官网： www.jeecg.com
版权所属： 北京国炬信息技术有限公司
公司官网： www.guojusoft.com


2025-08-06 19:23:00.787 [background-preinit] [34mINFO [0;39m [36morg.hibernate.validator.internal.util.Version:21[0;39m - HV000001: Hibernate Validator 6.2.5.Final
2025-08-06 19:23:00.863 [main] [34mINFO [0;39m [36morg.jeecg.JeecgSystemApplication:55[0;39m - Starting JeecgSystemApplication v3.7.3 using Java 1.8.0_144 on iZbp1215uww18su8x3o8hzZ with PID 345761 (/skypigeon/jeecg-system-start-3.7.3.jar started by root in /skypigeon)
2025-08-06 19:23:00.865 [main] [34mINFO [0;39m [36morg.jeecg.JeecgSystemApplication:638[0;39m - The following 1 profile is active: "prod"
2025-08-06 19:23:07.537 [main] [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate:262[0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-06 19:23:07.544 [main] [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate:132[0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-06 19:23:07.805 [main] [34mINFO [0;39m [36mo.s.d.r.config.RepositoryConfigurationDelegate:201[0;39m - Finished Spring Data repository scanning in 225 ms. Found 0 Redis repository interfaces.
2025-08-06 19:23:08.180 [main] [34mINFO [0;39m [36mo.j.minidao.auto.MinidaoAutoConfiguration:23[0;39m -  ******************* init miniDao config [ begin ] *********************** 
2025-08-06 19:23:08.182 [main] [34mINFO [0;39m [36mo.j.minidao.auto.MinidaoAutoConfiguration:25[0;39m -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
2025-08-06 19:23:08.184 [main] [34mINFO [0;39m [36mo.j.minidao.auto.MinidaoAutoConfiguration:42[0;39m -  *******************  init miniDao config  [ end ] *********************** 
2025-08-06 19:23:08.361 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportCategoryDao }
2025-08-06 19:23:08.362 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-08-06 19:23:08.363 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-08-06 19:23:08.364 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-08-06 19:23:08.365 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-08-06 19:23:08.365 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-08-06 19:23:08.366 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-08-06 19:23:08.366 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-08-06 19:23:08.367 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportJobDao }
2025-08-06 19:23:08.367 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportLogDao }
2025-08-06 19:23:08.367 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-08-06 19:23:08.368 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-08-06 19:23:08.368 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-08-06 19:23:08.368 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.JimuDragCategoryDao }
2025-08-06 19:23:08.369 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.JimuDragMapDao }
2025-08-06 19:23:08.369 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.JimuReportIconLibDao }
2025-08-06 19:23:08.370 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }
2025-08-06 19:23:08.370 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }
2025-08-06 19:23:08.371 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }
2025-08-06 19:23:08.372 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }
2025-08-06 19:23:08.373 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }
2025-08-06 19:23:08.373 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }
2025-08-06 19:23:08.373 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }
2025-08-06 19:23:08.374 [main] [34mINFO [0;39m [36mo.j.minidao.factory.MiniDaoClassPathMapperScanner:55[0;39m - register minidao name is { org.jeecg.modules.drag.dao.OnlDragShareDao }
2025-08-06 19:23:09.362 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.371 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.439 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.444 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.455 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.462 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.470 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.473 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.475 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.545 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.555 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:09.558 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$456/393996856] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:10.063 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:10.309 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:10.330 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:10.335 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$abcb07b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:11.534 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:11.551 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$ce00770f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:11.589 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:11.954 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$c92d17e4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:12.087 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:12.549 [main] [34mINFO [0;39m [36morg.jeecg.config.shiro.ShiroConfig:306[0;39m - ===============(1)创建缓存管理器RedisCacheManager
2025-08-06 19:23:12.561 [main] [34mINFO [0;39m [36morg.jeecg.config.shiro.ShiroConfig:327[0;39m - ===============(2)创建RedisManager,连接Redis..
2025-08-06 19:23:12.573 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:12.592 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:12.675 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:12.775 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$5fa79d6b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:12.787 [main] [34mINFO [0;39m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376[0;39m - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 19:23:13.742 [main] [34mINFO [0;39m [36mo.s.boot.web.embedded.tomcat.TomcatWebServer:108[0;39m - Tomcat initialized with port(s): 8080 (http)
2025-08-06 19:23:13.907 [main] [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext:292[0;39m - Root WebApplicationContext: initialization completed in 12917 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
2025-08-06 19:23:17.057 [main] [34mINFO [0;39m [36mcom.alibaba.druid.pool.DruidDataSource:673[0;39m - {dataSource-1,master} inited
2025-08-06 19:23:17.117 [main] [34mINFO [0;39m [36mcom.alibaba.druid.pool.DruidDataSource:673[0;39m - {dataSource-2,saige-ruoyi} inited
2025-08-06 19:23:17.118 [main] [34mINFO [0;39m [36mc.b.dynamic.datasource.DynamicRoutingDataSource:154[0;39m - dynamic-datasource - add a datasource named [master] success
2025-08-06 19:23:17.119 [main] [34mINFO [0;39m [36mc.b.dynamic.datasource.DynamicRoutingDataSource:154[0;39m - dynamic-datasource - add a datasource named [saige-ruoyi] success
2025-08-06 19:23:17.120 [main] [34mINFO [0;39m [36mc.b.dynamic.datasource.DynamicRoutingDataSource:237[0;39m - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
Registered plugin: 'org.jeecg.config.mybatis.MybatisInterceptor@410ae9a3'
Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1d1f7216'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/collecting/mapper/xml/CollectingPigeonsMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/demo/assOperator/mapper/xml/AssOperatorMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/demo/locationRingPool/mapper/xml/LocationRingPoolMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/demo/locationRingPool/mapper/xml/LocationRingPoolMxMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/demo/mqttlog/mapper/xml/SkypigeonDataLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/demo/raceconfig/mapper/xml/DeviceRaceconfigNewMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/message/mapper/xml/SysMessageMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/message/mapper/xml/SysMessageTemplateMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/pigeon/mapper/xml/PigeonMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/quartz/mapper/xml/QuartzJobMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/recycle/mapper/xml/RecycleBinMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/ruoyi/device/mapper/xml/GpsPigeonRacehistoryMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/ruoyi/mapper/xml/DeviceRaceconfigMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/ruoyi/mapper/xml/GpsDeviceMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/ruoyi/mapper/xml/GpsPigeonRaceMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/AssociationMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/BobMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/DeviceMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/DeviceRecordsMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/ElectronicAnkleBraceletMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/EquipmentCodeProductionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/EquipmentInfoMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/EquipmentManagementMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/HomingMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/LocationRingMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/MemberMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/NationalLegRingMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/OrderManageMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/PackageMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/PedalServerMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/PigeonEleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/SimulationTrainingGradeMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/SkypigeonBonusMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/SkypigeonEventsMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/SkypigeonGradeMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/SkypigeonMatchesMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/SkypigeonSplitCompetitionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/StNbdatascRMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/VipEquipmentMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/skypigeon/src/main/java/org/jeecg/modules/demo/skypigeon/mapper/xml/VipWriteMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysAnnouncementMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysAnnouncementSendMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysCategoryMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysCheckRuleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysCommentMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDataLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDataSourceMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDepartMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDepartPermissionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDepartRoleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDepartRolePermissionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDepartRoleUserMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDictItemMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysDictMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysFillRuleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysGatewayRouteMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysPackPermissionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysPermissionDataRuleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysPermissionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysPositionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysRoleIndexMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysRoleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysTableWhiteListMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysTenantMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysTenantPackMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysTenantPackUserMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysThirdAccountMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysThirdAppConfigMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysUserAgentMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysUserDepartMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysUserMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysUserPositionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/system/mapper/xml/SysUserTenantMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/training/mapper/xml/SimulationTrainingJoinMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-system-biz-3.7.3.jar!/org/jeecg/modules/training/mapper/xml/SimulationTrainingMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/auth/mapper/xml/OnlAuthDataMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/auth/mapper/xml/OnlAuthPageMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/auth/mapper/xml/OnlAuthRelationMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/cgform/mapper/xml/OnlCgformFieldMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/cgform/mapper/xml/OnlCgformHeadMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/cgform/mapper/xml/OnlCgformIndexMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/cgform/mapper/xml/OnlineMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/cgreport/mapper/xml/OnlCgreportHeadMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/cgreport/mapper/xml/OnlCgreportItemMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/hibernate-re-3.7.1-RC.jar!/org/jeecg/modules/online/cgreport/mapper/xml/OnlCgreportParamMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-module-demo-3.7.3.jar!/org/jeecg/modules/demo/test/mapper/xml/JeecgDemoMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-module-demo-3.7.3.jar!/org/jeecg/modules/demo/test/mapper/xml/JeecgOrderCustomerMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-module-demo-3.7.3.jar!/org/jeecg/modules/demo/test/mapper/xml/JeecgOrderMainMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-module-demo-3.7.3.jar!/org/jeecg/modules/demo/test/mapper/xml/JeecgOrderTicketMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-module-demo-3.7.3.jar!/org/jeecg/modules/demo/test/mapper/xml/JoaDemoMapper.xml]'
Parsed mapper file: 'URL [jar:file:/skypigeon/jeecg-system-start-3.7.3.jar!/BOOT-INF/lib/jeecg-boot-base-core-3.7.3.jar!/org/jeecg/modules/base/mapper/xml/BaseCommonMapper.xml]'
Initialization Sequence datacenterId:3 workerId:17
2025-08-06 19:23:22.846 [main] [34mINFO [0;39m [36morg.jeecg.common.modules.redis.config.RedisConfig:58[0;39m -  --- redis config init --- 
2025-08-06 19:23:26.963 [main] [34mINFO [0;39m [36morg.quartz.impl.StdSchedulerFactory:1220[0;39m - Using default implementation for ThreadExecutor
2025-08-06 19:23:26.970 [main] [34mINFO [0;39m [36morg.quartz.simpl.SimpleThreadPool:268[0;39m - Job execution threads will use class loader of thread: main
2025-08-06 19:23:26.999 [main] [34mINFO [0;39m [36morg.quartz.core.SchedulerSignalerImpl:61[0;39m - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-06 19:23:27.000 [main] [34mINFO [0;39m [36morg.quartz.core.QuartzScheduler:229[0;39m - Quartz Scheduler v.2.3.2 created.
2025-08-06 19:23:27.008 [main] [34mINFO [0;39m [36mo.s.scheduling.quartz.LocalDataSourceJobStore:672[0;39m - Using db table-based data access locking (synchronization).
2025-08-06 19:23:27.013 [main] [34mINFO [0;39m [36mo.s.scheduling.quartz.LocalDataSourceJobStore:145[0;39m - JobStoreCMT initialized.
2025-08-06 19:23:27.016 [main] [34mINFO [0;39m [36morg.quartz.core.QuartzScheduler:294[0;39m - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'iZbp1215uww18su8x3o8hzZ1754479406969'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-06 19:23:27.016 [main] [34mINFO [0;39m [36morg.quartz.impl.StdSchedulerFactory:1374[0;39m - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-06 19:23:27.017 [main] [34mINFO [0;39m [36morg.quartz.impl.StdSchedulerFactory:1378[0;39m - Quartz scheduler version: 2.3.2
2025-08-06 19:23:27.017 [main] [34mINFO [0;39m [36morg.quartz.core.QuartzScheduler:2293[0;39m - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2787de58
2025-08-06 19:23:37.090 [main] [34mINFO [0;39m [36mo.j.config.shiro.ignore.IgnoreAuthPostProcessor:41[0;39m - Init Token ignoreAuthUrls Config [ 集合 ]  ：[/openapi/demo/index, /skypigeon/payment/wechat/notify, /skypigeon/locationRing/registerLocationRing, /test/jeecgDemo/html, /skypigeon/skypigeonEvents/app/getHistoryEventsInfo, /customCgreport/customCgreport/getColumnsAndData/{typeId}, /member/member/wechatCode, /member/member/add, /test/pay/config, /Electronic/electronicAnkleBracelet/queryByEquipmentNumber]
2025-08-06 19:23:37.103 [main] [34mINFO [0;39m [36mo.j.config.shiro.ignore.IgnoreAuthPostProcessor:49[0;39m - Init Token ignoreAuthUrls Config [ 耗时 ] ：23毫秒
2025-08-06 19:23:37.948 [main] [34mINFO [0;39m [36mo.s.b.actuate.endpoint.web.EndpointLinksResolver:58[0;39m - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-06 19:23:38.305 [main] [34mINFO [0;39m [36morg.jeecg.config.init.CodeGenerateDbConfig:50[0;39m -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-08-06 19:23:38.415 [main] [34mINFO [0;39m [36ms.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69[0;39m - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-06 19:23:41.358 [main] [34mINFO [0;39m [36mo.s.boot.web.embedded.tomcat.TomcatWebServer:220[0;39m - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-08-06 19:23:41.363 [main] [34mINFO [0;39m [36ms.d.s.web.plugins.DocumentationPluginsBootstrapper:93[0;39m - Documentation plugins bootstrapped
2025-08-06 19:23:41.375 [main] [34mINFO [0;39m [36ms.d.s.web.plugins.DocumentationPluginsBootstrapper:79[0;39m - Found 1 custom documentation plugin(s)
2025-08-06 19:23:42.265 [main] [34mINFO [0;39m [36ms.d.spring.web.scanners.ApiListingReferenceScanner:44[0;39m - Scanning for api listing references
2025-08-06 19:23:42.889 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_1
2025-08-06 19:23:42.948 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_1
2025-08-06 19:23:42.953 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_1
2025-08-06 19:23:42.957 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_1
2025-08-06 19:23:42.963 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_1
2025-08-06 19:23:42.966 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_1
2025-08-06 19:23:43.001 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_2
2025-08-06 19:23:43.018 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_1
2025-08-06 19:23:43.044 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_3
2025-08-06 19:23:43.054 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_2
2025-08-06 19:23:43.058 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_2
2025-08-06 19:23:43.061 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_2
2025-08-06 19:23:43.066 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_2
2025-08-06 19:23:43.069 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_2
2025-08-06 19:23:43.079 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_2
2025-08-06 19:23:43.135 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_4
2025-08-06 19:23:43.221 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_3
2025-08-06 19:23:43.230 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_3
2025-08-06 19:23:43.233 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_3
2025-08-06 19:23:43.237 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_3
2025-08-06 19:23:43.240 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_3
2025-08-06 19:23:43.288 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_3
2025-08-06 19:23:43.332 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_5
2025-08-06 19:23:43.346 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_4
2025-08-06 19:23:43.357 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_4
2025-08-06 19:23:43.360 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_4
2025-08-06 19:23:43.364 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_4
2025-08-06 19:23:43.366 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_4
2025-08-06 19:23:43.375 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_4
2025-08-06 19:23:43.416 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_6
2025-08-06 19:23:43.435 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_5
2025-08-06 19:23:43.439 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_5
2025-08-06 19:23:43.444 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_5
2025-08-06 19:23:43.448 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_5
2025-08-06 19:23:43.451 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_5
2025-08-06 19:23:43.460 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_5
2025-08-06 19:23:43.490 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_7
2025-08-06 19:23:43.503 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_6
2025-08-06 19:23:43.510 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_6
2025-08-06 19:23:43.513 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_6
2025-08-06 19:23:43.517 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_6
2025-08-06 19:23:43.519 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_6
2025-08-06 19:23:43.527 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_6
2025-08-06 19:23:43.606 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_8
2025-08-06 19:23:43.656 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_7
2025-08-06 19:23:43.665 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_7
2025-08-06 19:23:43.669 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_7
2025-08-06 19:23:43.673 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_7
2025-08-06 19:23:43.676 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_7
2025-08-06 19:23:43.715 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_7
2025-08-06 19:23:43.743 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_9
2025-08-06 19:23:43.756 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_8
2025-08-06 19:23:43.760 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_8
2025-08-06 19:23:43.765 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_8
2025-08-06 19:23:43.769 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_8
2025-08-06 19:23:43.771 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_8
2025-08-06 19:23:43.783 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_8
2025-08-06 19:23:43.807 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_10
2025-08-06 19:23:43.815 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_9
2025-08-06 19:23:43.819 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_9
2025-08-06 19:23:43.823 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_9
2025-08-06 19:23:43.828 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_9
2025-08-06 19:23:43.830 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_9
2025-08-06 19:23:43.837 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_9
2025-08-06 19:23:43.867 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_11
2025-08-06 19:23:43.876 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_10
2025-08-06 19:23:43.879 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_10
2025-08-06 19:23:43.882 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_10
2025-08-06 19:23:43.885 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_10
2025-08-06 19:23:43.887 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_10
2025-08-06 19:23:43.951 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_10
2025-08-06 19:23:43.997 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_11
2025-08-06 19:23:44.001 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_11
2025-08-06 19:23:44.005 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_11
2025-08-06 19:23:44.009 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_11
2025-08-06 19:23:44.011 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_11
2025-08-06 19:23:44.033 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_11
2025-08-06 19:23:44.113 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_12
2025-08-06 19:23:44.182 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_12
2025-08-06 19:23:44.187 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_12
2025-08-06 19:23:44.191 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_12
2025-08-06 19:23:44.204 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_12
2025-08-06 19:23:44.206 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_12
2025-08-06 19:23:44.220 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: webAddUsingPOST_1
2025-08-06 19:23:44.246 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_12
2025-08-06 19:23:44.356 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_13
2025-08-06 19:23:44.380 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_13
2025-08-06 19:23:44.394 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_13
2025-08-06 19:23:44.398 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_13
2025-08-06 19:23:44.408 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_13
2025-08-06 19:23:44.409 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_13
2025-08-06 19:23:44.528 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_14
2025-08-06 19:23:44.534 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_14
2025-08-06 19:23:44.545 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_14
2025-08-06 19:23:44.549 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_14
2025-08-06 19:23:44.552 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_14
2025-08-06 19:23:44.555 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_14
2025-08-06 19:23:44.583 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_13
2025-08-06 19:23:44.615 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_15
2025-08-06 19:23:44.619 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_15
2025-08-06 19:23:44.623 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_15
2025-08-06 19:23:44.626 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_15
2025-08-06 19:23:44.630 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_15
2025-08-06 19:23:44.633 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_15
2025-08-06 19:23:44.649 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_14
2025-08-06 19:23:44.671 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_16
2025-08-06 19:23:44.680 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_16
2025-08-06 19:23:44.686 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_16
2025-08-06 19:23:44.692 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_16
2025-08-06 19:23:44.697 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_16
2025-08-06 19:23:44.699 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_16
2025-08-06 19:23:44.708 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_15
2025-08-06 19:23:44.736 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_17
2025-08-06 19:23:44.745 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_17
2025-08-06 19:23:44.748 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_17
2025-08-06 19:23:44.752 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_17
2025-08-06 19:23:44.756 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_17
2025-08-06 19:23:44.759 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_16
2025-08-06 19:23:44.776 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_18
2025-08-06 19:23:44.785 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_18
2025-08-06 19:23:44.789 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_18
2025-08-06 19:23:44.793 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_18
2025-08-06 19:23:44.799 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_18
2025-08-06 19:23:44.800 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_17
2025-08-06 19:23:44.810 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_17
2025-08-06 19:23:44.830 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_19
2025-08-06 19:23:44.839 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_19
2025-08-06 19:23:44.843 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_19
2025-08-06 19:23:44.846 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_19
2025-08-06 19:23:44.849 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_19
2025-08-06 19:23:44.851 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_18
2025-08-06 19:23:44.864 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_18
2025-08-06 19:23:44.870 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_20
2025-08-06 19:23:44.892 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_20
2025-08-06 19:23:44.898 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_20
2025-08-06 19:23:44.900 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_21
2025-08-06 19:23:44.903 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_20
2025-08-06 19:23:44.906 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_20
2025-08-06 19:23:44.908 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_19
2025-08-06 19:23:44.913 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_19
2025-08-06 19:23:44.936 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_21
2025-08-06 19:23:44.943 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_21
2025-08-06 19:23:44.946 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_22
2025-08-06 19:23:44.948 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_21
2025-08-06 19:23:44.951 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_21
2025-08-06 19:23:44.953 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_20
2025-08-06 19:23:44.973 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_20
2025-08-06 19:23:45.038 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_22
2025-08-06 19:23:45.104 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_22
2025-08-06 19:23:45.109 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_23
2025-08-06 19:23:45.113 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_22
2025-08-06 19:23:45.119 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_22
2025-08-06 19:23:45.121 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_21
2025-08-06 19:23:45.127 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editdzUsingPUT_1
2025-08-06 19:23:45.129 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editdzUsingPOST_1
2025-08-06 19:23:45.155 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_21
2025-08-06 19:23:45.231 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_23
2025-08-06 19:23:45.236 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_23
2025-08-06 19:23:45.241 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_24
2025-08-06 19:23:45.245 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_23
2025-08-06 19:23:45.250 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_23
2025-08-06 19:23:45.252 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPOST_22
2025-08-06 19:23:45.260 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryByIdUsingGET_22
2025-08-06 19:23:45.281 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: queryPageListUsingGET_24
2025-08-06 19:23:45.289 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: addUsingPOST_24
2025-08-06 19:23:45.292 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteUsingDELETE_25
2025-08-06 19:23:45.294 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: deleteBatchUsingDELETE_24
2025-08-06 19:23:45.298 [main] [34mINFO [0;39m [36ms.d.s.w.r.operation.CachingOperationNameGenerator:41[0;39m - Generating unique operation named: editUsingPUT_24
