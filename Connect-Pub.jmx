<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <!-- Test Plan -->
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="MQTT Load Test Plan" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
        <collectionProp name="Arguments.arguments">
          <elementProp name="server" elementType="Argument">
            <stringProp name="Argument.name">server</stringProp>
            <stringProp name="Argument.value">10.211.55.5</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="port" elementType="Argument">
            <stringProp name="Argument.name">port</stringProp>
            <stringProp name="Argument.value">1883</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="clientIdPrefix" elementType="Argument">
            <stringProp name="Argument.name">clientIdPrefix</stringProp>
            <stringProp name="Argument.value">cid_</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="connKeepAlive" elementType="Argument">
            <stringProp name="Argument.name">connKeepAlive</stringProp>
            <stringProp name="Argument.value">60</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="threadCount" elementType="Argument">
            <stringProp name="Argument.name">threadCount</stringProp>
            <stringProp name="Argument.value">100</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="rampUp" elementType="Argument">
            <stringProp name="Argument.name">rampUp</stringProp>
            <stringProp name="Argument.value">10</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="loopCount" elementType="Argument">
            <stringProp name="Argument.name">loopCount</stringProp>
            <stringProp name="Argument.value">100</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="publishInterval" elementType="Argument">
            <stringProp name="Argument.name">publishInterval</stringProp>
            <stringProp name="Argument.value">100</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      
      <!-- Connection Group -->
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Connection Group" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">1</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">${threadCount}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${rampUp}</stringProp>
        <longProp name="ThreadGroup.start_time">0</longProp>
        <longProp name="ThreadGroup.end_time">0</longProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
      </ThreadGroup>
      <hashTree>
        <!-- MQTT Connect -->
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect" enabled="true">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.client_id_prefix">${clientIdPrefix}</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.conn_keep_alive">${connKeepAlive}</stringProp>
          <stringProp name="mqtt.version">3.1</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
          <boolProp name="mqtt.conn_share">false</boolProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <!-- MQTT Disconnect -->
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect" enabled="true"/>
        <hashTree/>
      </hashTree>
      
      <!-- Publish Group -->
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Publish Group" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">${loopCount}</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">${threadCount}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${rampUp}</stringProp>
        <longProp name="ThreadGroup.start_time">0</longProp>
        <longProp name="ThreadGroup.end_time">0</longProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
      </ThreadGroup>
      <hashTree>
        <!-- MQTT Connect -->
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect" enabled="true">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.client_id_prefix">${clientIdPrefix}</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.conn_keep_alive">${connKeepAlive}</stringProp>
          <stringProp name="mqtt.version">3.1</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
          <boolProp name="mqtt.conn_share">false</boolProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        
        <!-- **修正后的** MQTT Publish -->
        <net.xmeter.samplers.PubSampler guiclass="net.xmeter.gui.PubSamplerUI" testclass="net.xmeter.samplers.PubSampler" testname="MQTT Publish" enabled="true">
          <stringProp name="mqtt.topic_name">test/topic/${__threadNum}</stringProp>
          <stringProp name="mqtt.payload">{"value":${__Random(1,100)}}</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.retained">false</boolProp>
        </net.xmeter.samplers.PubSampler>
        <hashTree/>
        
        <!-- 发布间隔 -->
        <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="Publish Interval Timer" enabled="true">
          <stringProp name="ConstantTimer.delay">${publishInterval}</stringProp>
        </ConstantTimer>
        <hashTree/>
        
        <!-- MQTT Disconnect -->
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect" enabled="true"/>
        <hashTree/>
      </hashTree>
      
      <!-- 结果监听器 -->
      <ResultCollector guiclass="org.apache.jmeter.visualizers.SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time><latency>true</latency><timestamp>true</timestamp>
            <success>true</success><label>true</label><code>true</code><message>true</message>
            <threadName>true</threadName><dataType>true</dataType>
            <assertions>true</assertions><subresults>true</subresults>
            <bytes>true</bytes><sentBytes>true</sentBytes>
          </value>
        </objProp>
      </ResultCollector>
      <hashTree/>
      
      <ResultCollector guiclass="org.apache.jmeter.visualizers.AggregateReport" testclass="ResultCollector" testname="Aggregate Report" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time><latency>true</latency><timestamp>true</timestamp>
            <success>true</success><label>true</label><code>true</code><message>true</message>
            <threadName>true</threadName><dataType>true</dataType>
            <assertions>true</assertions><subresults>true</subresults>
            <bytes>true</bytes><sentBytes>true</sentBytes>
          </value>
        </objProp>
      </ResultCollector>
      <hashTree/>
      
    </hashTree>
  </hashTree>
</jmeterTestPlan>